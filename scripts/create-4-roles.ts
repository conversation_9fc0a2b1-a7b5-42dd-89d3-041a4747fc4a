import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function create4Roles() {
  try {
    console.log('🔧 Creating the 4 specific roles...');

    // Check if roles already exist
    const existingRoles = await (prisma as any).role.findMany({});

    if (existingRoles.length === 4) {
      console.log('✅ All 4 roles already exist, skipping creation');
      console.log('📋 Existing roles:');
      for (const role of existingRoles) {
        console.log(`  - ${role.name} (ID: ${role.id})`);
      }
      return;
    }

    // Delete all existing roles first
    const deletedRoles = await prisma.role.deleteMany({});
    console.log(`🗑️  Deleted ${deletedRoles.count} existing roles`);

    // Create the 4 specific roles
    const rolesToCreate = [
      { name: 'admin' },
      { name: 'user_r' },
      { name: 'user_w' },
      { name: 'user_d' }
    ];

    // Create roles
    for (const role of rolesToCreate) {
      const createdRole = await (prisma as any).role.create({
        data: role
      });
      console.log(`✅ Created role: ${createdRole.name} (${createdRole.id})`);
    }
    
    console.log('🎉 All 4 roles created successfully!');
    
    // Show final roles
    const allRoles = await prisma.role.findMany({});
    console.log('\n📋 Final roles in database:');
    allRoles.forEach(role => {
      console.log(`  - ${role.name} (ID: ${role.id})`);
    });
    
  } catch (error) {
    console.error('❌ Error creating roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

create4Roles();
