import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function resetDatabase() {
  try {
    console.log('🗑️  Cleaning database...');
    
    // Delete all users
    const deletedUsers = await prisma.user.deleteMany({});
    console.log(`🗑️  Deleted ${deletedUsers.count} users`);
    
    // Delete all organizations
    const deletedOrganizations = await prisma.organization.deleteMany({});
    console.log(`🗑️  Deleted ${deletedOrganizations.count} organizations`);
    
    // Delete all roles
    const deletedRoles = await prisma.role.deleteMany({});
    console.log(`🗑️  Deleted ${deletedRoles.count} roles`);
    
    // Delete all sessions
    const deletedSessions = await prisma.session.deleteMany({});
    console.log(`🗑️  Deleted ${deletedSessions.count} sessions`);
    
    // Delete all refresh tokens
    const deletedTokens = await prisma.refreshToken.deleteMany({});
    console.log(`🗑️  Deleted ${deletedTokens.count} refresh tokens`);
    
    console.log('✅ Database cleaned successfully');
    
    // Create default roles
    console.log('🔧 Creating default roles...');
    
    const defaultRoles = [
      {
        name: 'admin',
        user_r: true,
        user_w: true,
        user_d: true,
      },
      {
        name: 'user',
        user_r: true,
        user_w: false,
        user_d: false,
      },
      {
        name: 'manager',
        user_r: true,
        user_w: true,
        user_d: false,
      }
    ];

    for (const role of defaultRoles) {
      const createdRole = await prisma.role.create({
        data: role
      });
      console.log(`✅ Created role: ${createdRole.name} (${createdRole.id})`);
    }
    
    console.log('🎉 Database reset completed successfully!');
    
  } catch (error) {
    console.error('❌ Error resetting database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetDatabase();
