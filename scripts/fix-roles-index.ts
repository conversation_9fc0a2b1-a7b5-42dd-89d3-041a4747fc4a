import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixRolesIndex() {
  try {
    console.log('🔧 Fixing roles collection index...');
    
    // Drop the old index
    try {
      await prisma.$runCommandRaw({
        dropIndexes: 'roles',
        index: 'role_type_unique'
      });
      console.log('✅ Dropped old role_type_unique index');
    } catch (error) {
      console.log('ℹ️  Old index not found or already dropped');
    }
    
    // Create the new index
    try {
      await prisma.$runCommandRaw({
        createIndexes: 'roles',
        indexes: [
          {
            key: { name: 1 },
            name: 'role_name_unique',
            unique: true,
            background: true
          }
        ]
      });
      console.log('✅ Created new role_name_unique index');
    } catch (error) {
      console.log('ℹ️  New index already exists');
    }
    
    // Delete all existing roles
    const deletedRoles = await prisma.role.deleteMany({});
    console.log(`🗑️  Deleted ${deletedRoles.count} existing roles`);
    
    // Create default roles
    console.log('🔧 Creating default roles...');
    
    const defaultRoles = [
      {
        name: 'admin',
        user_r: true,
        user_w: true,
        user_d: true,
      },
      {
        name: 'user',
        user_r: true,
        user_w: false,
        user_d: false,
      },
      {
        name: 'manager',
        user_r: true,
        user_w: true,
        user_d: false,
      }
    ];

    for (const role of defaultRoles) {
      const createdRole = await prisma.role.create({
        data: role
      });
      console.log(`✅ Created role: ${createdRole.name} (${createdRole.id})`);
    }
    
    console.log('🎉 Roles setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixRolesIndex();
