# API Gateway Services - Technical Guide

## 🏗️ System Architecture Overview

This API Gateway implements a complete authentication system with JWT tokens, Redis caching, and MongoDB persistence. The system follows a secure token-based authentication pattern with refresh token rotation.

### Core Components

1. **Authentication Service** - Handles user registration, login, and token management
2. **Database Service** - MongoDB connection with Prisma ORM
3. **Redis Service** - Token blacklisting and session caching
4. **Email Service** - User verification and notifications
5. **Rate Limiting** - Protection against abuse
6. **Multi-domain Support** - CORS and cookie configuration

## 🔐 Authentication Flow

### Token System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Access Token  │    │  Refresh Token  │    │  Redis Cache    │
│   (15 minutes)  │    │   (1-5 days)    │    │ (Blacklisting)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MongoDB       │
                    │ (User Data)     │
                    └─────────────────┘
```

### Security Features

- **JWT Access Tokens**: Short-lived (15 minutes) for API access
- **Refresh Tokens**: Longer-lived (1-5 days) for token renewal
- **Token Blacklisting**: Redis-based invalidation
- **Rate Limiting**: Protection against brute force attacks
- **Email Verification**: Required for account activation
- **Secure Cookies**: HttpOnly, Secure, SameSite protection

## 🏁 Project Setup Checklist

### First Time Setup

1. **Clone and install:**
   ```bash
   git clone <repository>
   cd api-gateway-services
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your MongoDB, Redis, and SMTP settings
   ```

3. **Generate Prisma client (CRITICAL):**
   ```bash
   npx prisma generate
   ```

4. **Start development server:**
   ```bash
   npm run dev
   ```

### Development Workflow

**When you modify the database schema:**
```bash
# 1. Edit prisma/schema.prisma
# 2. Regenerate client
npx prisma generate

# 3. Push changes to database (development)
npx prisma db push

# 4. Restart your server
npm run dev
```

**When switching branches:**
```bash
git checkout feature/new-schema
# Check if schema changed, then:
npx prisma generate  # If schema was modified
npm run dev
```

## 📋 Step-by-Step User Registration & Authentication

### Step 1: User Registration

**Endpoint:** `POST /auth/register`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email for verification.",
  "data": {
    "user": {
      "id": "user_id_here",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "isEmailVerified": false
    }
  }
}
```

**What happens internally:**
1. Password validation (min 8 chars, uppercase, lowercase, number, special char)
2. Email uniqueness check
3. Password hashing with bcrypt (12 rounds)
4. User creation in MongoDB
5. Email verification token generation
6. Verification email sent

### Step 2: Email Verification

**Endpoint:** `POST /auth/verify`

**Request:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQi..."
}
```

**Process:**
1. Frontend/client sends verification token via POST request
2. System validates token and expiration
3. User account is activated (`isEmailVerified: true`)
4. User can now login

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### Step 3: User Login

**Endpoint:** `POST /auth/login`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "rememberMe": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id_here",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "isEmailVerified": true,
      "lastLogin": "2024-01-15T10:30:00.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-15T10:45:00.000Z",
    "expiresIn": 900
  }
}
```

**What happens internally:**
1. Email and password validation
2. User lookup in database
3. Password verification with bcrypt
4. Access token generation (15 minutes)
5. Refresh token generation (1 day or 5 days if rememberMe)
6. Tokens stored in secure cookies
7. User session tracking

## 🔑 Using Protected Endpoints

### Authentication Methods

You can authenticate using either:

1. **Authorization Header** (Recommended for APIs):
```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

2. **Secure Cookies** (Automatic for web apps):
```
accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Example: Get User Profile

**Endpoint:** `GET /auth/profile`

**cURL Example:**
```bash
curl -X GET http://localhost:8080/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "id": "user_id_here",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "isEmailVerified": true,
      "isActive": true,
      "lastLogin": "2024-01-15T10:30:00.000Z",
      "createdAt": "2024-01-10T08:00:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

## 🔄 Token Refresh Process

### When Access Token Expires

**Endpoint:** `POST /auth/refresh`

**Request (with refresh token in body):**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Or use cookies (automatic):**
```bash
curl -X POST http://localhost:8080/auth/refresh \
  -H "Content-Type: application/json" \
  --cookie "refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-15T11:00:00.000Z",
    "expiresIn": 900
  }
}
```

**What happens internally:**
1. Refresh token validation
2. Blacklist check in Redis
3. New access token generation
4. New refresh token generation (rotation)
5. Old refresh token blacklisted
6. New tokens stored in cookies

## 🚪 Logout Process

**Endpoint:** `POST /auth/logout`

**Request:**
```bash
curl -X POST http://localhost:8080/auth/logout \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE"
```

**What happens internally:**
1. Extract tokens from request
2. Add both tokens to Redis blacklist
3. Clear secure cookies
4. Invalidate session

## 📊 Session Management

### Get Active Sessions

**Endpoint:** `GET /auth/sessions`

**Response:**
```json
{
  "success": true,
  "message": "Sessions retrieved successfully",
  "data": {
    "sessions": [
      {
        "id": "session_id_1",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "expiresAt": "2024-01-16T10:30:00.000Z"
      }
    ]
  }
}
```

## 🔍 Token Validation

### Validate Access Token

**Endpoint:** `POST /auth/validate`

**Request:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token is valid",
  "data": {
    "expiresAt": "2024-01-15T10:45:00.000Z",
    "expiresIn": 300
  }
}
```

## 🛡️ Error Handling

### Common Error Responses

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Access token is required",
  "error": {
    "code": "UNAUTHORIZED"
  }
}
```

**403 Forbidden:**
```json
{
  "success": false,
  "message": "Token has been revoked",
  "error": {
    "code": "TOKEN_REVOKED"
  }
}
```

**429 Too Many Requests:**
```json
{
  "success": false,
  "message": "Too many requests. Please try again later.",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED"
  }
}
```

## 🔧 Development Testing

### Using Swagger UI

1. **Access Swagger:** http://localhost:8080/docs
2. **Register a user** via `/auth/register`
3. **Verify email** (check console logs for verification link)
4. **Login** via `/auth/login`
5. **Copy access token** from response
6. **Click "Authorize"** in Swagger UI
7. **Paste token** in format: `Bearer YOUR_TOKEN_HERE`
8. **Test protected endpoints**

### Using cURL Scripts

**Complete Registration Flow:**
```bash
# 1. Register
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "firstName": "Test",
    "lastName": "User"
  }'

# 2. Verify email (get token from email/logs)
curl -X GET http://localhost:8080/auth/verify/VERIFICATION_TOKEN_HERE

# 3. Login
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123!"
  }'

# 4. Use protected endpoint
curl -X GET http://localhost:8080/auth/profile \
  -H "Authorization: Bearer ACCESS_TOKEN_HERE"
```

## 🌐 Multi-Domain Configuration

### Supported Domains

- **Development:** `localhost`
- **Production:** `locura.com`, `*.locura.com`, `dalecosta.com`, `*.dalecosta.com`

### CORS Configuration

The system automatically handles cross-origin requests for configured domains with wildcard subdomain support.

## 📈 Monitoring & Health Checks

### Health Endpoints

- **`GET /health`** - Basic health check
- **`GET /ready`** - Readiness check (database + Redis)

### Rate Limiting

- **Login attempts:** 5 per 15 minutes per IP
- **Registration:** 3 per hour per IP
- **General API:** 100 requests per 15 minutes per IP

## 🔒 Security Best Practices

1. **Always use HTTPS** in production
2. **Set secure environment variables**
3. **Disable Swagger** in production (`ENABLE_SWAGGER=false`)
4. **Monitor rate limits** and failed login attempts
5. **Regularly rotate JWT secrets**
6. **Use strong passwords** for database and Redis
7. **Keep dependencies updated**

## 🔧 Database Setup & Prisma Client

### Initial Setup (REQUIRED)

After cloning the project or modifying the database schema, you MUST generate the Prisma client:

```bash
# Generate Prisma client (REQUIRED)
npx prisma generate
```

### When to run `npx prisma generate`

**🚨 MANDATORY situations:**
```bash
# After cloning the project
git clone <repository>
cd api-gateway-services
npm install
npx prisma generate  # ← REQUIRED before starting

# After modifying prisma/schema.prisma
# (any changes to models, fields, relations)
npx prisma generate  # ← REQUIRED before using code

# After switching branches with schema changes
git checkout feature/new-models
npx prisma generate  # ← REQUIRED if schema changed

# In CI/CD pipeline
npm ci
npx prisma generate  # ← REQUIRED in build process
npm run build
```

**💡 RECOMMENDED situations:**
```bash
# After Prisma updates
npm update @prisma/client prisma
npx prisma generate  # ← Good practice

# Before production deployment
npx prisma generate  # ← Safety check
npm run build
```

### What does `npx prisma generate` do?

1. **Reads your schema:** Analyzes `prisma/schema.prisma`
2. **Generates TypeScript types:** Creates type-safe interfaces for all models
3. **Creates client methods:** Generates optimized database operations
4. **Maps collections:** Links Prisma models to MongoDB collections
   - `User` model → `users` collection
   - `Session` model → `sessions` collection
   - `RefreshToken` model → `refresh_tokens` collection

### Database Collections

Our MongoDB database (`api_gateway_db`) contains these collections:

- **`users`** - User accounts and authentication data
- **`sessions`** - Active user sessions
- **`refresh_tokens`** - JWT refresh tokens
- **`blacklisted_tokens`** - Revoked/invalid tokens

### Available Prisma Commands

```bash
# Generate client (most important)
npx prisma generate

# Push schema to database (development)
npx prisma db push

# Open database GUI
npx prisma studio

# Reset database (development only)
npx prisma db push --force-reset

# Available npm scripts
npm run prisma:generate  # Generate client
npm run prisma:push      # Push schema
npm run prisma:studio    # Open GUI
```

### Troubleshooting Prisma Issues

**Error: "Cannot find module '@prisma/client'"**
```bash
# Solution: Generate the client
npx prisma generate
```

**Error: "Type 'User' does not exist"**
```bash
# Solution: Regenerate after schema changes
npx prisma generate
```

**Error: "Property 'user' does not exist on type 'PrismaClient'"**
```bash
# Solution: Generate client after adding new models
npx prisma generate
```

## 🚀 Production Deployment

### Environment Variables Checklist

```bash
# Required for production
NODE_ENV=production
ENABLE_SWAGGER=false
JWT_SECRET=your-super-secure-secret-here
JWT_REFRESH_SECRET=your-refresh-secret-here
DATABASE_URL=your-production-mongodb-url
REDIS_URL=your-production-redis-url

# Email configuration
SMTP_HOST=your-smtp-server
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Domain configuration
COOKIE_ALLOWED_DOMAINS=".yourdomain.com"
CORS_ORIGINS="https://yourdomain.com,https://*.yourdomain.com"
```

## 🧪 Practical Testing Examples

### Frontend Integration Example (JavaScript)

```javascript
class ApiClient {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
    this.accessToken = localStorage.getItem('accessToken');
  }

  async register(userData) {
    const response = await fetch(`${this.baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });
    return response.json();
  }

  async login(email, password, rememberMe = false) {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include', // Include cookies
      body: JSON.stringify({ email, password, rememberMe })
    });

    const data = await response.json();
    if (data.success) {
      this.accessToken = data.data.accessToken;
      localStorage.setItem('accessToken', this.accessToken);
    }
    return data;
  }

  async apiCall(endpoint, options = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      credentials: 'include'
    });

    // Handle token refresh if needed
    if (response.status === 401) {
      const refreshed = await this.refreshToken();
      if (refreshed) {
        // Retry original request
        return this.apiCall(endpoint, options);
      }
    }

    return response.json();
  }

  async refreshToken() {
    try {
      const response = await fetch(`${this.baseUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include'
      });

      const data = await response.json();
      if (data.success) {
        this.accessToken = data.data.accessToken;
        localStorage.setItem('accessToken', this.accessToken);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }
    return false;
  }

  async logout() {
    await fetch(`${this.baseUrl}/auth/logout`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.accessToken}` },
      credentials: 'include'
    });

    this.accessToken = null;
    localStorage.removeItem('accessToken');
  }
}

// Usage example
const api = new ApiClient();

// Register new user
await api.register({
  email: '<EMAIL>',
  password: 'SecurePass123!',
  firstName: 'John',
  lastName: 'Doe'
});

// Login
await api.login('<EMAIL>', 'SecurePass123!');

// Make authenticated requests
const profile = await api.apiCall('/auth/profile');
const sessions = await api.apiCall('/auth/sessions');
```

### Python Integration Example

```python
import requests
import json

class ApiClient:
    def __init__(self, base_url='http://localhost:8080'):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token = None

    def register(self, email, password, first_name=None, last_name=None):
        data = {
            'email': email,
            'password': password,
            'firstName': first_name,
            'lastName': last_name
        }
        response = self.session.post(f'{self.base_url}/auth/register', json=data)
        return response.json()

    def login(self, email, password, remember_me=False):
        data = {
            'email': email,
            'password': password,
            'rememberMe': remember_me
        }
        response = self.session.post(f'{self.base_url}/auth/login', json=data)
        result = response.json()

        if result.get('success'):
            self.access_token = result['data']['accessToken']
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}'
            })

        return result

    def api_call(self, endpoint, method='GET', data=None):
        url = f'{self.base_url}{endpoint}'
        response = self.session.request(method, url, json=data)

        # Handle token refresh
        if response.status_code == 401:
            if self.refresh_token():
                response = self.session.request(method, url, json=data)

        return response.json()

    def refresh_token(self):
        response = self.session.post(f'{self.base_url}/auth/refresh')
        result = response.json()

        if result.get('success'):
            self.access_token = result['data']['accessToken']
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}'
            })
            return True
        return False

    def logout(self):
        self.session.post(f'{self.base_url}/auth/logout')
        self.access_token = None
        self.session.headers.pop('Authorization', None)

# Usage
api = ApiClient()
api.register('<EMAIL>', 'SecurePass123!', 'John', 'Doe')
api.login('<EMAIL>', 'SecurePass123!')
profile = api.api_call('/auth/profile')
```

## 🔧 Troubleshooting Guide

### Common Issues and Solutions

#### 1. "Access token is required"
**Problem:** Missing or invalid Authorization header
**Solution:**
```bash
# Correct format
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# NOT this
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 2. "Token has been revoked"
**Problem:** Token is blacklisted in Redis
**Solution:** Login again to get new tokens

#### 3. "Email not verified"
**Problem:** User trying to login without email verification
**Solution:** Check email for verification link or resend verification

#### 4. "Rate limit exceeded"
**Problem:** Too many requests from same IP
**Solution:** Wait for rate limit window to reset (15 minutes)

#### 5. CORS Errors
**Problem:** Frontend domain not in allowed origins
**Solution:** Add domain to `CORS_ORIGINS` environment variable

#### 6. Cookie Issues
**Problem:** Cookies not being set/sent
**Solution:**
- Ensure `credentials: 'include'` in fetch requests
- Check domain configuration in `COOKIE_ALLOWED_DOMAINS`
- Verify HTTPS in production

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=true
LOG_LEVEL=debug
```

### Health Check Debugging

```bash
# Check system health
curl http://localhost:8080/health

# Check readiness (database + Redis)
curl http://localhost:8080/ready

# Expected healthy response
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "environment": "development",
  "version": "1.0.0"
}
```

### Database Connection Issues

```bash
# Test MongoDB connection
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'

# If database is down, you'll get:
{
  "success": false,
  "message": "Database connection failed"
}
```

### Redis Connection Issues

```bash
# Test Redis (try login - refresh tokens use Redis)
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'

# If Redis is down, login might work but refresh will fail
```

## 📝 API Response Patterns

### Success Response Format
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": ["Additional error details"]
  }
}
```

### Validation Error Format
```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": [
      "Password must be at least 8 characters long",
      "Email format is invalid"
    ]
  }
}
```

This comprehensive guide provides everything needed to understand and integrate with the authentication system.
