// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Embedded type for organization roles
type OrganizationRole {
  organizationId String @map("_organization_id") @db.ObjectId
  roleIds        String[] @map("_role_ids_fk") @db.ObjectId
}

model User {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  organizationIds   String[] @map("_organization_ids_fk") @db.ObjectId // Keep for backward compatibility
  organizationsRoles OrganizationRole[] @map("_organizations_roles") // New structure
  email             String   @unique
  password          String
  firstName         String
  lastName          String
  phoneNumber       String?
  isEmailVerified   <PERSON>ole<PERSON>  @default(false)
  emailVerificationToken String?
  emailVerificationExpires DateTime?
  isActive          Boolean  @default(true)
  lastLogin         DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations - Note: Many-to-many relationship with organizations
  sessions          Session[]
  refreshTokens     RefreshToken[]

  @@map("users")
}

model Organization {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  vatNumber String   @map("VAT_Number")
  address   Address
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations - Note: Many-to-many relationship with users
  @@map("organizations")
}

model Role {
  id   String @id @default(auto()) @map("_id") @db.ObjectId
  name String @unique

  @@map("roles")
}

type Address {
  street     String
  city       String
  postalCode String
  country    String
}

model Session {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  token     String   @unique
  expiresAt DateTime
  isActive  Boolean  @default(true)
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model RefreshToken {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  token     String   @unique
  expiresAt DateTime
  isActive  Boolean  @default(true)
  sessionId String?  @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model BlacklistedToken {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  token     String   @unique
  expiresAt DateTime
  reason    String?  // logout, ban, etc.
  createdAt DateTime @default(now())

  @@map("blacklisted_tokens")
}
