# API Gateway Services

A complete TypeScript backend with advanced authentication system, built with Fastify, MongoDB, Redis and JWT.

## 🚀 Features

- **Complete JWT Authentication** with access token and refresh token
- **User Registration** with email verification
- **Secure Login** with "Remember Me" option (sessions up to 5 days)
- **Automatic Token Refresh** when expired
- **Session Management** with Redis for optimal performance
- **Token Blacklisting** for secure logout and user banning
- **Rate Limiting** for attack protection
- **Secure Cookies** with httpOnly, secure, sameSite configurations
- **Robust Validation** with Zod
- **MongoDB Database** with Prisma ORM
- **Email Verification** with HTML templates
- **Complete Security Middleware**
- **Structured Logging** with <PERSON><PERSON>
- **Health Checks** for monitoring
- **Graceful Shutdown** for safe deployments

## 🛠️ Technology Stack

- **Runtime**: Node.js + TypeScript
- **Framework**: Fastify
- **Database**: MongoDB with Prisma ORM
- **Cache**: Redis
- **Authentication**: JWT (jsonwebtoken)
- **Validation**: Zod
- **Email**: Nodemailer
- **Password**: bcryptjs
- **Logging**: <PERSON><PERSON> (integrated in Fastify)

## 📋 Prerequisites

- Node.js 18+
- MongoDB 5.0+
- Redis 6.0+
- SMTP server for email sending (Gmail, SendGrid, etc.)

## ⚡ Quick Installation

1. **Clone the repository**
```bash
git clone https://gitlab.com/dale-labs/dev/be-services/api-gateway-services.git
cd api-gateway-services
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your configurations
```

4. **Generate Prisma client (REQUIRED)**
```bash
npx prisma generate
# or
npm run prisma:generate
```

5. **Start in development mode**
```bash
npm run dev
```

## 🔧 Prisma Client Management

### When to run `npx prisma generate`:

**REQUIRED situations:**
- ✅ After cloning the project
- ✅ After modifying `prisma/schema.prisma`
- ✅ After switching branches with schema changes
- ✅ Before building for production

**Commands:**
```bash
# Generate Prisma client
npx prisma generate

# Push schema to database (development)
npx prisma db push

# Open Prisma Studio (database GUI)
npx prisma studio

# Available npm scripts
npm run prisma:generate  # Generate client
npm run prisma:push      # Push schema to DB
npm run prisma:studio    # Open Prisma Studio
```

**Important:** Always run `npx prisma generate` after schema changes, otherwise TypeScript will show errors!

The server will be available at `http://localhost:3000`

## 🔧 Configuration

### Essential Environment Variables

```env
# Server
PORT=3000
NODE_ENV=development
HOST=localhost

# Database MongoDB
DATABASE_URL="mongodb://localhost:27017/api_gateway_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT Secrets (CAMBIA IN PRODUZIONE!)
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-jwt-key"

# Cookie Security
COOKIE_SECRET="your-super-secret-cookie-key"
COOKIE_SECURE=false  # true in produzione con HTTPS
COOKIE_SAME_SITE="lax"

# Email SMTP
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# Swagger Documentation (optional)
ENABLE_SWAGGER=true
SWAGGER_HOST="localhost:3000"
```

### MongoDB Configuration

The system automatically creates the necessary collections on first startup:
- `users` - User data
- `sessions` - Active sessions
- `refresh_tokens` - Refresh tokens
- `blacklisted_tokens` - Invalidated tokens

### Redis Configuration

Redis is used for:
- JWT token blacklisting
- Session caching
- Rate limiting
- Temporary data

## 📚 API Endpoints

### Authentication

| Endpoint | Method | Description | Auth |
|----------|--------|-------------|------|
| `/auth/register` | POST | Register new user | ❌ |
| `/auth/login` | POST | User login | ❌ |
| `/auth/logout` | POST | Logout and invalidate token | ✅ |
| `/auth/refresh` | POST | Refresh access token | ❌ |
| `/auth/validate` | POST | External token validation | ❌ |
| `/auth/verify` | POST | Verify user email | ❌ |
| `/auth/profile` | GET | Current user profile | ✅ |
| `/auth/sessions` | GET | User active sessions | ✅ |

### System

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | API Info |
| `/health` | GET | Health check |
| `/ready` | GET | Readiness check |
| `/docs` | GET | Swagger API Documentation |

## 🔐 Usage Examples

### User Registration

```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "Mario",
    "lastName": "Rossi"
  }'
```

### Login

```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "rememberMe": true
  }'
```

### External Token Validation

```bash
curl -X POST http://localhost:3000/auth/validate \
  -H "Content-Type: application/json" \
  -d '{
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

### Access with Token

```bash
curl -X GET http://localhost:3000/auth/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 🔄 System Operation

### Authentication Flow

1. **Registration**: User registers → receives verification email
2. **Email Verification**: Click on link → account activated
3. **Login**: Valid credentials → receives access token + refresh token
4. **API Access**: Uses access token for authenticated calls
5. **Automatic Refresh**: Token expired → uses refresh token for new access token
6. **Logout**: Invalidation of all tokens in Redis

### Email Verification

Email verification is handled through a single API endpoint:

**API Endpoint:**
```
POST /auth/verify
Content-Type: application/json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQi..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### Token Management

- **Access Token**: Duration 15 minutes (5 days with "Remember Me")
- **Refresh Token**: Duration 7 days
- **Auto-refresh**: Middleware intercepts expired tokens and renews them automatically
- **Blacklist**: Invalidated tokens saved in Redis until natural expiration

### Security

- **Password**: bcrypt hash with 12 rounds
- **Cookies**: httpOnly, secure (HTTPS), sameSite
- **Rate Limiting**: Brute force protection
- **Validation**: Input sanitized with Zod
- **CORS**: Configured for specific domains

## 🚀 Deployment

### Production

1. **Configure production environment variables**
```env
NODE_ENV=production
COOKIE_SECURE=true
DATABASE_URL="mongodb://prod-server:27017/api_gateway_prod"
# Other production values...
```

2. **Build the application**
```bash
npm run build
```

3. **Start in production**
```bash
npm start
```

### Docker (Optional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 Testing

```bash
# Connection test
curl http://localhost:3000/health

# Registration test
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

## 📊 Monitoring

### Health Checks

- **`/health`**: General status + database/Redis connections
- **`/ready`**: Readiness for load balancer
- **Structured logs**: JSON with configurable levels

### Available Metrics

- Server uptime
- Database connection status
- Redis connection status
- Rate limit per IP/user
- Active sessions

## 🔧 Available Scripts

```bash
npm run dev          # Development with hot reload
npm run build        # TypeScript build
npm start            # Production start
npm run prisma:generate  # Generate Prisma client
npm run prisma:studio    # Prisma database UI
```

## 👥 Authors

- **Dale Costa** - *Initial development* - [dale-labs](https://gitlab.com/dale-labs)
