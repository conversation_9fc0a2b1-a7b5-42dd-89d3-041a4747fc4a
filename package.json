{"name": "api-gateway-services", "version": "1.0.0", "description": "API Gateway Services with Authentication - TypeScript, Fastify, MongoDB, Redis", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+ssh://**************/dale-labs/dev/be-services/api-gateway-services.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://gitlab.com/dale-labs/dev/be-services/api-gateway-services/issues"}, "homepage": "https://gitlab.com/dale-labs/dev/be-services/api-gateway-services#readme", "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "dotenv": "^17.0.1", "fastify": "^5.4.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.4", "prisma": "^6.11.1", "redis": "^5.5.6", "zod": "^3.25.74"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}