import { FastifyRequest, FastifyReply } from 'fastify';
import { redisService } from '@/config/redis';
import { config } from '@/config/environment';
import { RateLimitInfo } from '@/types/auth';

interface RateLimitOptions {
  windowMs: number;
  max: number;
  keyGenerator?: (request: FastifyRequest) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
}

export function createRateLimit(options: RateLimitOptions) {
  const {
    windowMs = config.security.rateLimitWindowMs,
    max = config.security.rateLimitMax,
    keyGenerator = (req) => req.ip,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    message = 'Too many requests, please try again later.',
  } = options;

  return async function rateLimitMiddleware(request: FastifyRequest, reply: FastifyReply) {
    try {
      const key = keyGenerator(request);
      const redisKey = `rate_limit:${key}`;

      // Get current count
      const current = await redisService.incrementRateLimit(redisKey, windowMs);
      
      // Calculate reset time
      const resetTime = new Date(Date.now() + windowMs);
      
      // Set rate limit headers
      reply.header('X-RateLimit-Limit', max);
      reply.header('X-RateLimit-Remaining', Math.max(0, max - current));
      reply.header('X-RateLimit-Reset', Math.ceil(resetTime.getTime() / 1000));

      if (current > max) {
        reply.header('Retry-After', Math.ceil(windowMs / 1000));
        
        return reply.status(429).send({
          success: false,
          message,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            details: {
              limit: max,
              windowMs,
              retryAfter: Math.ceil(windowMs / 1000),
            }
          }
        });
      }

      // Store rate limit info in request for potential use
      request.rateLimit = {
        limit: max,
        remaining: Math.max(0, max - current),
        reset: resetTime,
      };

    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // Continue without rate limiting if Redis is down
    }
  };
}

// Predefined rate limiters
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  keyGenerator: (req) => `auth:${req.ip}`,
  message: 'Too many authentication attempts, please try again later.',
});

export const registerRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes (reduced for development)
  max: 10, // 10 registrations per window (increased for development)
  keyGenerator: (req) => `register:${req.ip}`,
  message: 'Too many registration attempts, please try again later.',
});

export const emailRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 email sends per hour
  keyGenerator: (req) => {
    const body = req.body as any;
    return `email:${body?.email || req.ip}`;
  },
  message: 'Too many email requests, please try again later.',
});

export const apiRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  keyGenerator: (req) => `api:${req.user?.id || req.ip}`,
  message: 'Too many API requests, please try again later.',
});

export const strictRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 requests per window
  keyGenerator: (req) => `strict:${req.ip}`,
  message: 'Rate limit exceeded, please try again later.',
});

// Declare rate limit info in FastifyRequest
declare module 'fastify' {
  interface FastifyRequest {
    rateLimit?: RateLimitInfo;
  }
}

export default {
  createRateLimit,
  authRateLimit,
  registerRateLimit,
  emailRateLimit,
  apiRateLimit,
  strictRateLimit,
};
