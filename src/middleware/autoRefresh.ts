import { FastifyRequest, FastifyReply } from 'fastify';
import { JWTService } from '@/utils/jwt';
import { authService } from '@/services/authService';
import { config } from '@/config/environment';

export async function autoRefreshMiddleware(request: FastifyRequest, reply: FastifyReply) {
  try {
    // Get access token from Authorization header or cookies
    let accessToken: string | undefined;
    
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (request.cookies.accessToken) {
      accessToken = request.cookies.accessToken;
    }

    if (!accessToken) {
      // No access token, continue without refresh
      return;
    }

    // Verify access token
    const validation = await JWTService.verifyAccessToken(accessToken);
    
    // If token is valid, continue
    if (validation.isValid) {
      return;
    }

    // If token is not expired or blacklisted, don't try to refresh
    if (!validation.isExpired || validation.isBlacklisted) {
      return;
    }

    // Token is expired, try to refresh using refresh token
    const refreshToken = request.cookies.refreshToken;
    if (!refreshToken) {
      // No refresh token available, can't auto-refresh
      return;
    }

    console.log('Access token expired, attempting auto-refresh...');

    // Attempt to refresh tokens
    const refreshResult = await authService.refreshTokens(refreshToken);
    
    if (refreshResult.status && refreshResult.data?.accessToken && refreshResult.data?.refreshToken) {
      console.log('Auto-refresh successful');

      // Update cookies with new tokens
      const cookieOptions = {
        httpOnly: true,
        secure: config.cookie.secure,
        sameSite: config.cookie.sameSite as 'strict' | 'lax' | 'none',
        domain: config.cookie.domain,
        path: '/',
      };

      // Set new access token cookie
      const accessTokenExpires = refreshResult.data.expiresAt || new Date(Date.now() + 15 * 60 * 1000);
      reply.setCookie('accessToken', refreshResult.data.accessToken, {
        ...cookieOptions,
        expires: accessTokenExpires,
      });

      // Set new refresh token cookie
      const refreshTokenExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      reply.setCookie('refreshToken', refreshResult.data.refreshToken, {
        ...cookieOptions,
        expires: refreshTokenExpires,
      });

      // Update request headers with new access token for downstream middleware
      request.headers.authorization = `Bearer ${refreshResult.data.accessToken}`;
      
      // Also update cookies in request for immediate use
      request.cookies.accessToken = refreshResult.data.accessToken;
      request.cookies.refreshToken = refreshResult.data.refreshToken;

      // Add refresh info to response headers
      reply.header('X-Token-Refreshed', 'true');
      reply.header('X-New-Token-Expires', accessTokenExpires.toISOString());

      console.log('Auto-refresh completed, new tokens set');
    } else {
      console.log('Auto-refresh failed:', refreshResult.message);
      
      // Clear invalid cookies
      reply.clearCookie('accessToken', {
        domain: config.cookie.domain,
        path: '/',
      });
      reply.clearCookie('refreshToken', {
        domain: config.cookie.domain,
        path: '/',
      });
      reply.clearCookie('rememberMe', {
        domain: config.cookie.domain,
        path: '/',
      });
    }
  } catch (error) {
    console.error('Auto-refresh middleware error:', error);
    // Continue without refresh on error
  }
}

// Middleware specifically for API routes that should auto-refresh
export async function apiAutoRefreshMiddleware(request: FastifyRequest, reply: FastifyReply) {
  try {
    await autoRefreshMiddleware(request, reply);
    
    // After potential refresh, check if we have a valid token
    let accessToken: string | undefined;
    
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (request.cookies.accessToken) {
      accessToken = request.cookies.accessToken;
    }

    if (accessToken) {
      const validation = await JWTService.verifyAccessToken(accessToken);
      
      if (!validation.isValid) {
        // Even after refresh attempt, token is still invalid
        if (validation.isExpired) {
          return reply.status(401).send({
            status: false,
            code: '401',
            message: 'Session expired. Please login again. Auto-refresh failed or refresh token expired',
            data: null
          });
        } else if (validation.isBlacklisted) {
          return reply.status(401).send({
            status: false,
            code: '401',
            message: 'Token has been revoked, please login again',
            data: null
          });
        } else {
          return reply.status(401).send({
            status: false,
            code: '401',
            message: 'Invalid token, please logi again',
            data: null
          });
        }
      }
    }
  } catch (error) {
    console.error('API auto-refresh middleware error:', error);
    return reply.status(500).send({
      status: false,
      code: '500',
      message: 'Authentication error',
      data: null
    });
  }
}

// Optional middleware that attempts refresh but doesn't fail if no token
export async function optionalAutoRefreshMiddleware(request: FastifyRequest, reply: FastifyReply) {
  try {
    await autoRefreshMiddleware(request, reply);
  } catch (error) {
    console.warn('Optional auto-refresh middleware error:', error);
    // Continue silently on error for optional auth
  }
}

export default {
  autoRefreshMiddleware,
  apiAutoRefreshMiddleware,
  optionalAutoRefreshMiddleware,
};
