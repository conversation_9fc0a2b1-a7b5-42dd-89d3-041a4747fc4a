import { FastifyRequest, FastifyReply } from 'fastify';
import { JWTService } from '@/utils/jwt';
import { databaseService } from '@/config/database';
import { redisService } from '@/config/redis';

export async function authenticateToken(request: FastifyRequest, reply: FastifyReply) {
  try {
    // Get token from Authorization header or cookies
    let token: string | undefined;
    
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (request.cookies.accessToken) {
      token = request.cookies.accessToken;
    }

    if (!token) {
      return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Access token is required',
        data: null
      });
    }

    // Verify token
    const validation = await JWTService.verifyAccessToken(token);
    
    if (!validation.isValid) {
      if (validation.isBlacklisted) {
        return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Token has been revoked',
        data: null
      });
      }
      
      if (validation.isExpired) {
        return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Token has expired',
        data: null
      });
      }
      
      return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Invalid token',
        data: null
      });
    }

    const payload = validation.payload!;

    // Get session data from Redis (contains complete user data)
    const sessionData = await redisService.getSession(payload.sessionId);

    if (!sessionData || !sessionData.user) {
      // Session not found in Redis - fallback to database
      const prisma = databaseService.getClient();
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          isActive: true,
          isEmailVerified: true,
        }
      });

      if (!user || !user.isActive) {
        return reply.status(401).send({
          status: false,
          code: "401",
          message: 'User not found or inactive',
          data: null
        });
      }

      // Session expired or missing - user needs to login again
      return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Session expired, please login again',
        data: null
      });
    }

    // Verify user is still active (from session data)
    if (!sessionData.user.isActive) {
      return reply.status(401).send({
        status: false,
        code: "401",
        message: 'User account is inactive',
        data: null
      });
    }

    // Verify session exists and is active
    const prisma = databaseService.getClient();
    const session = await prisma.session.findUnique({
      where: { id: payload.sessionId },
      select: {
        id: true,
        userId: true,
        isActive: true,
        expiresAt: true,
      }
    });

    if (!session || !session.isActive || session.expiresAt < new Date()) {
      return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Session not found or expired',
        data: null
      });
    }

    // Attach user and session info to request (using raw session data)
    request.user = {
      id: sessionData.user.id,
      email: sessionData.user.email,
      sessionId: session.id,
    };

    // Store full raw user data in a custom property for easy access
    (request as any).userData = sessionData.user; // Raw user data from MongoDB
    (request as any).sessionData = sessionData;

    request.session = {
      id: session.id,
      userId: session.userId,
      isRemembered: false, // This would be determined by session data
    };

    request.tokenPayload = payload;

    // Update last activity in session cache
    try {
      if (sessionData) {
        sessionData.lastActivity = new Date();

        // Calculate TTL based on session expiration and isRemembered status
        const sessionExpiresAt = session.expiresAt.getTime();
        const now = Date.now();
        const remainingTTL = Math.max(0, Math.floor((sessionExpiresAt - now) / 1000));

        // Preserve sessionCreatedAt and update the cache with correct TTL
        await redisService.storeSession(
          session.id,
          sessionData,
          remainingTTL // Use remaining session time
        );
      }
    } catch (error) {
      console.warn('Failed to update session activity:', error);
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return reply.status(500).send({
        status: false,
        code: "500",
        message: 'Internal authentication error',
        data: null
      });
  }
}

export async function optionalAuth(request: FastifyRequest, reply: FastifyReply) {
  try {
    // Get token from Authorization header or cookies
    let token: string | undefined;
    
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (request.cookies.accessToken) {
      token = request.cookies.accessToken;
    }

    if (!token) {
      // No token provided, continue without authentication
      return;
    }

    // Verify token
    const validation = await JWTService.verifyAccessToken(token);
    
    if (validation.isValid && validation.payload) {
      const payload = validation.payload;

      // Verify user exists and is active
      const prisma = databaseService.getClient();
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          isActive: true,
          isEmailVerified: true,
        }
      });

      if (user && user.isActive) {
        // Verify session exists and is active
        const session = await prisma.session.findUnique({
          where: { id: payload.sessionId },
          select: {
            id: true,
            userId: true,
            isActive: true,
            expiresAt: true,
          }
        });

        if (session && session.isActive && session.expiresAt >= new Date()) {
          // Attach user and session info to request
          request.user = {
            id: user.id,
            email: user.email,
            sessionId: session.id,
          };

          request.session = {
            id: session.id,
            userId: session.userId,
            isRemembered: false,
          };

          request.tokenPayload = payload;
        }
      }
    }
  } catch (error) {
    console.warn('Optional authentication error:', error);
    // Continue without authentication
  }
}

export async function requireEmailVerification(request: FastifyRequest, reply: FastifyReply) {
  if (!request.user) {
    return reply.status(401).send({
        status: false,
        code: "401",
        message: 'Authentication required',
        data: null
      });
  }

  const prisma = databaseService.getClient();
  const user = await prisma.user.findUnique({
    where: { id: request.user.id },
    select: { isEmailVerified: true }
  });

  if (!user || !user.isEmailVerified) {
    return reply.status(403).send({
        status: false,
        code: "403",
        message: 'Email verification required',
        data: null
      });
  }
}

export default {
  authenticateToken,
  optionalAuth,
  requireEmailVerification,
};
