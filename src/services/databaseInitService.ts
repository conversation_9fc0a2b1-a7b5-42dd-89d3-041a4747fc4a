import { PrismaClient } from '@prisma/client';
import { config } from '@/config/environment';

export class DatabaseInitService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Initialize database with required collections and indexes
   */
  async initializeDatabase(): Promise<void> {
    if (!config.database.autoCreate) {
      console.log('Database auto-creation is disabled');
      return;
    }

    console.log('🔧 Initializing database structure...');

    try {
      // Test basic connection
      await this.testConnection();

      // Create indexes for better performance
      await this.createIndexes();

      // Verify collections exist (MongoDB will create them automatically on first insert)
      await this.verifyCollections();

      console.log('✅ Database initialization completed successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    try {
      // Simple connection test - this will create the database if it doesn't exist
      await this.prisma.$connect();
      console.log('📊 Database connection established');
    } catch (error) {
      console.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Create necessary indexes for optimal performance
   */
  private async createIndexes(): Promise<void> {
    try {
      console.log('📋 Creating database indexes...');

      // Users collection indexes
      await this.prisma.$runCommandRaw({
        createIndexes: 'users',
        indexes: [
          {
            key: { email: 1 },
            name: 'email_unique',
            unique: true,
            background: true
          },
          {
            key: { emailVerificationToken: 1 },
            name: 'emailVerificationToken_index',
            background: true,
            sparse: true
          },
          {
            key: { isActive: 1, isEmailVerified: 1 },
            name: 'user_status_index',
            background: true
          },
          {
            key: { _organization_ids_fk: 1 },
            name: 'organizations_index',
            background: true
          },
          {
            key: { _role_ids_fk: 1 },
            name: 'roles_index',
            background: true
          },
          {
            key: { createdAt: 1 },
            name: 'createdAt_index',
            background: true
          }
        ]
      });

      // Organizations collection indexes
      await this.prisma.$runCommandRaw({
        createIndexes: 'organizations',
        indexes: [
          {
            key: { name: 1 },
            name: 'organization_name_index',
            background: true
          },
          {
            key: { 'VAT_Number': 1 },
            name: 'vat_number_index',
            background: true
          }
        ]
      });

      // Roles collection indexes
      await this.prisma.$runCommandRaw({
        createIndexes: 'roles',
        indexes: [
          {
            key: { name: 1 },
            name: 'role_name_unique',
            unique: true,
            background: true
          }
        ]
      });

      console.log('✅ Database indexes created successfully');
    } catch (error) {
      // Indexes might already exist, which is fine
      if (error instanceof Error && error.message.includes('already exists')) {
        console.log('📋 Database indexes already exist');
      } else {
        console.warn('⚠️  Warning: Could not create some indexes:', error);
        // Don't throw here as the app can still function without optimal indexes
      }
    }
  }

  /**
   * Verify that collections exist and are accessible
   */
  private async verifyCollections(): Promise<void> {
    try {
      console.log('🔍 Verifying database collections...');

      // Try to count documents in collections (this will create them if they don't exist)
      const userCount = await this.prisma.user.count();
      const organizationCount = await this.prisma.organization.count();
      const roleCount = await this.prisma.role.count();

      console.log(`📊 users collection verified (${userCount} users)`);
      console.log(`📊 organizations collection verified (${organizationCount} organizations)`);
      console.log(`📊 roles collection verified (${roleCount} roles)`);

      // Initialize default roles if none exist
      await this.initializeDefaultRoles();

      console.log('✅ All collections verified');
    } catch (error) {
      console.error('Failed to verify collections:', error);
      throw error;
    }
  }

  /**
   * Initialize default roles
   */
  private async initializeDefaultRoles(): Promise<void> {
    try {
      const existingRoles = await this.prisma.role.count();

      if (existingRoles === 0) {
        console.log('🔧 Creating default roles...');

        const defaultRoles = [
          { name: 'admin' },
          { name: 'user_r' },
          { name: 'user_w' },
          { name: 'user_d' }
        ];

        for (const role of defaultRoles) {
          await this.prisma.role.create({
            data: role
          });
        }

        console.log('✅ Default roles created successfully');
      } else {
        console.log('📋 Default roles already exist');
      }
    } catch (error) {
      console.warn('⚠️  Warning: Could not create default roles:', error);
    }
  }

  /**
   * Get admin role ID
   */
  async getAdminRoleId(): Promise<string> {
    const adminRole = await this.prisma.role.findUnique({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      throw new Error('Admin role not found. Please run database initialization.');
    }

    return adminRole.id;
  }

  /**
   * Create a test user to verify everything works (development only)
   */
  async createTestUser(): Promise<void> {
    if (config.server.nodeEnv !== 'development') {
      return;
    }

    // Temporarily disabled until we complete the schema migration
    console.log('👤 Test user creation temporarily disabled during schema migration');
    return;

    /*
    try {
      const testEmail = '<EMAIL>';

      // Check if test user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: testEmail }
      });

      if (existingUser) {
        console.log('👤 Test user already exists');
        return;
      }

      // Get admin role
      const adminRole = await this.prisma.role.findUnique({
        where: { type: 'admin' }
      });

      if (!adminRole) {
        console.warn('⚠️  Admin role not found, skipping test user creation');
        return;
      }

      // Create test organization
      const testOrganization = await this.prisma.organization.create({
        data: {
          name: 'Test Organization',
          vatNumber: 'TEST123456789',
          address: {
            street: 'Test Street 123',
            city: 'Test City',
            postalCode: '12345',
            country: 'Test Country'
          }
        }
      });

      // Create test user
      const testUser = await this.prisma.user.create({
        data: {
          email: testEmail,
          password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // "password123"
          firstName: 'Test',
          lastName: 'User',
          organizationId: testOrganization.id,
          roleId: adminRole.id,
          isEmailVerified: true,
          isActive: true,
        }
      });

      console.log('👤 Test user created:', testUser.email);
      console.log('🏢 Test organization created:', testOrganization.name);
    } catch (error) {
      console.warn('⚠️  Could not create test user:', error);
      // Don't throw as this is not critical
    }
    */
  }

  /**
   * Clean up test data (development only)
   */
  async cleanupTestData(): Promise<void> {
    if (config.server.nodeEnv !== 'development') {
      return;
    }

    try {
      await this.prisma.user.deleteMany({
        where: {
          email: {
            endsWith: '@example.com'
          }
        }
      });
      console.log('🧹 Test data cleaned up');
    } catch (error) {
      console.warn('⚠️  Could not clean up test data:', error);
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<any> {
    try {
      const stats = await this.prisma.$runCommandRaw({
        dbStats: 1
      });

      const userCount = await this.prisma.user.count();
      const activeUserCount = await this.prisma.user.count({
        where: { isActive: true }
      });
      const verifiedUserCount = await this.prisma.user.count({
        where: { isEmailVerified: true }
      });

      return {
        database: stats,
        collections: {
          users: {
            total: userCount,
            active: activeUserCount,
            verified: verifiedUserCount
          }
        }
      };
    } catch (error) {
      console.error('Failed to get database stats:', error);
      return null;
    }
  }
}
