import { PrismaClient } from '@prisma/client';
import { redisService } from '@/config/redis';
import { databaseService } from '@/config/database';
import { UserCacheData, OrganizationRole } from '@/types/auth';

export class UserCacheService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = databaseService.getClient();
  }

  /**
   * Carica e caches i dati completi dell'utente per una sessione
   */
  async cacheUserData(userId: string, sessionId: string, expiresIn: number): Promise<UserCacheData | null> {
    try {
      // Recupera dati utente completi dal database
      const user = await (this.prisma.user as any).findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          isEmailVerified: true,
          isActive: true,
          lastLogin: true,
          organizationIds: true,
          organizationsRoles: true,
        },
      });

      if (!user) {
        return null;
      }

      // Recupera dati delle organizzazioni
      const organizations = await (this.prisma.organization as any).findMany({
        where: {
          id: { in: user.organizationIds || [] }
        },
        select: {
          id: true,
          name: true,
          vatNumber: true,
        }
      });

      // Recupera tutti i roleIds dalle organizationsRoles
      const allRoleIds = (user.organizationsRoles || [])
        .flatMap((orgRole: OrganizationRole) => orgRole.roleIds);

      // Recupera dati dei ruoli
      const roles = await (this.prisma.role as any).findMany({
        where: {
          id: { in: allRoleIds }
        },
        select: {
          id: true,
          name: true,
          permissions: true,
        }
      });

      // Costruisci oggetto cache
      const userCacheData: UserCacheData = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        isEmailVerified: user.isEmailVerified,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        organizationIds: user.organizationIds || [],
        organizationsRoles: user.organizationsRoles || [],
        organizations,
        roles,
        cachedAt: new Date(),
      };

      // Salva in Redis
      await redisService.storeUserCache(sessionId, userCacheData, expiresIn);
      
      // Traccia la sessione per l'utente
      await redisService.addUserSession(userId, sessionId);

      console.log(`✅ User cache stored for session: ${sessionId}`);
      return userCacheData;

    } catch (error) {
      console.error('Failed to cache user data:', error);
      return null;
    }
  }

  /**
   * Recupera i dati utente dalla cache
   */
  async getUserFromCache(sessionId: string): Promise<UserCacheData | null> {
    try {
      const cachedData = await redisService.getUserCache(sessionId);
      
      if (cachedData) {
        // Controlla se la cache è ancora fresca (opzionale)
        const cacheAge = Date.now() - new Date(cachedData.cachedAt).getTime();
        const maxAge = 30 * 60 * 1000; // 30 minuti
        
        if (cacheAge > maxAge) {
          console.log(`🔄 Cache expired for session: ${sessionId}, refreshing...`);
          // Cache scaduta, ricarica
          return await this.refreshUserCache(sessionId);
        }
        
        return cachedData;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get user from cache:', error);
      return null;
    }
  }

  /**
   * Aggiorna la cache dell'utente
   */
  async refreshUserCache(sessionId: string): Promise<UserCacheData | null> {
    try {
      // Prima recupera i dati della sessione per ottenere userId
      const sessionData = await redisService.getSession(sessionId);
      if (!sessionData) {
        return null;
      }

      // Ricarica e caches i dati
      return await this.cacheUserData(sessionData.userId, sessionId, 24 * 60 * 60); // 24 ore
    } catch (error) {
      console.error('Failed to refresh user cache:', error);
      return null;
    }
  }

  /**
   * Rimuove la cache dell'utente per una sessione specifica
   */
  async clearUserCache(sessionId: string): Promise<void> {
    try {
      // Prima recupera userId dalla cache
      const cachedUser = await redisService.getUserCache(sessionId);
      
      if (cachedUser) {
        // Rimuovi la sessione dal tracking dell'utente
        await redisService.removeUserSession(cachedUser.id, sessionId);
      }
      
      // Rimuovi la cache
      await redisService.deleteUserCache(sessionId);
      
      console.log(`🗑️ User cache cleared for session: ${sessionId}`);
    } catch (error) {
      console.error('Failed to clear user cache:', error);
    }
  }

  /**
   * Rimuove tutte le cache per un utente (logout globale)
   */
  async clearAllUserCaches(userId: string): Promise<void> {
    try {
      await redisService.clearAllUserSessions(userId);
      console.log(`🗑️ All user caches cleared for user: ${userId}`);
    } catch (error) {
      console.error('Failed to clear all user caches:', error);
    }
  }

  /**
   * Invalida la cache quando i dati dell'utente cambiano
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      const sessions = await redisService.getUserSessions(userId);
      
      // Rimuovi tutte le cache per questo utente
      for (const sessionId of sessions) {
        await redisService.deleteUserCache(sessionId);
      }
      
      console.log(`♻️ User cache invalidated for user: ${userId}`);
    } catch (error) {
      console.error('Failed to invalidate user cache:', error);
    }
  }
}

export const userCacheService = new UserCacheService();
