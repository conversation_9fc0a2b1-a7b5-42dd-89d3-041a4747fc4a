import { PrismaClient } from '@prisma/client';
import { JWTService } from '@/utils/jwt';
import { PasswordService } from '@/utils/password';
import { databaseService } from '@/config/database';
import { redisService } from '@/config/redis';
import { config } from '@/config/environment';
import { LoginRequest, RegisterRequest, AuthResponse, SessionData, ReturnMessage } from '@/types/auth';
import { EmailService } from './emailService';

// Helper function to convert duration string to milliseconds
function durationToMs(duration: string): number {
  const match = duration.match(/^(\d+)([smhd])$/);
  if (!match) return 0;

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 's': return value * 1000;
    case 'm': return value * 60 * 1000;
    case 'h': return value * 60 * 60 * 1000;
    case 'd': return value * 24 * 60 * 60 * 1000;
    default: return 0;
  }
}

// Helper functions for standardized responses
const createSuccessResponse = <T>(message: string, code: string, data: T): ReturnMessage<T> => ({
  status: true,
  message,
  code,
  data
});

const createErrorResponse = (message: string, code: string, data: any = null): ReturnMessage<any> => ({
  status: false,
  message,
  code,
  data
});

export class AuthService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = databaseService.getClient();
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    try {
      // Validate password strength
      const passwordValidation = PasswordService.validatePasswordStrength(data.password);
      if (!passwordValidation.isValid) {
        return createErrorResponse(
          'Password does not meet security requirements',
          '400',
          { errors: passwordValidation.errors }
        );
      }

      // Check if password is compromised
      const isCompromised = await PasswordService.isPasswordCompromised(data.password);
      if (isCompromised) {
        return createErrorResponse(
          'This password has been found in data breaches. Please choose a different password.',
          '400'
        );
      }

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: data.email.toLowerCase() },
      });

      if (existingUser) {
        return createErrorResponse('User with this email already exists', '400');
      }

      // Check if organization with same VAT number exists
      const existingOrganization = await (this.prisma as any).organization.findFirst({
        where: { vatNumber: data.organization.vatNumber },
      });

      if (existingOrganization) {
        return createErrorResponse(
          `An organization with VAT number ${data.organization.vatNumber} already exists. If you belong to this organization, please contact your administrator to be added to the existing organization.`,
          '400'
        );
      }

      // Get role IDs - default to admin role for first user
      let roleIds = data.roleIds || [config.roles.adminId];

      if (!config.roles.adminId) {
        return createErrorResponse(
          'Admin role not configured. Please check environment variables.',
          '400'
        );
      }

      // Validate that all role IDs exist
      console.log('🔍 Validating role IDs:', roleIds);
      const existingRoles = await (this.prisma as any).role.findMany({
        where: {
          id: { in: roleIds }
        }
      });
      console.log('✅ Found existing roles:', existingRoles.length, 'out of', roleIds.length);

      if (existingRoles.length !== roleIds.length) {
        console.log('❌ Role validation failed');
        return createErrorResponse('One or more role IDs are invalid.', '400');
      }

      // Hash password
      const hashedPassword = await PasswordService.hashPassword(data.password);

      // Create organization first
      const organization = await (this.prisma as any).organization.create({
        data: {
          name: data.organization.name,
          vatNumber: data.organization.vatNumber,
          address: {
            street: data.address.street,
            city: data.address.city,
            postalCode: data.address.postalCode,
            country: data.address.country,
          },
        },
      });

      // Generate email verification token
      const emailVerificationToken = JWTService.generateEmailVerificationToken({
        userId: '', // Will be set after user creation
        email: data.email.toLowerCase(),
      });

      // Create user with new organization-role structure
      const user = await (this.prisma.user as any).create({
        data: {
          email: data.email.toLowerCase(),
          password: hashedPassword,
          firstName: data.firstName,
          lastName: data.lastName,
          phoneNumber: data.phoneNumber,
          organizationIds: [organization.id], // Keep for backward compatibility
          organizationsRoles: [
            {
              organizationId: organization.id,
              roleIds: roleIds,
            }
          ],
          emailVerificationToken,
          emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          organizationIds: true,
          organizationsRoles: true,
          isEmailVerified: true,
          createdAt: true,
        },
      });

      // Update verification token with actual user ID
      const finalVerificationToken = JWTService.generateEmailVerificationToken({
        userId: user.id,
        email: user.email,
      });

      await this.prisma.user.update({
        where: { id: user.id },
        data: { emailVerificationToken: finalVerificationToken },
      });

      // Send verification email
      try {
        await EmailService.sendVerificationEmail(user.email, finalVerificationToken, user.firstName || undefined);
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Don't fail registration if email fails
      }

      return createSuccessResponse(
        'User registered successfully. Please check your email to verify your account.',
        '201',
        {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName || undefined,
            lastName: user.lastName || undefined,
            isEmailVerified: user.isEmailVerified,
          },
        }
      );
    } catch (error) {
      console.error('Registration error:', error);
      return createErrorResponse('Registration failed. Please try again.', '400');
    }
  }

  async login(data: LoginRequest, ipAddress?: string, userAgent?: string): Promise<AuthResponse> {
    try {
      // Find user
      const user = await this.prisma.user.findUnique({
        where: { email: data.email.toLowerCase() },
      });

      if (!user) {
        return createErrorResponse('Invalid email or password', '400');
      }

      if (!user.isActive) {
        return createErrorResponse('Account is deactivated. Please contact support.', '400');
      }

      // Verify password
      const isPasswordValid = await PasswordService.verifyPassword(data.password, user.password);
      if (!isPasswordValid) {
        return createErrorResponse('Invalid email or password', '400');
      }

      // Create session with duration from config
      const isRemembered = data.rememberMe || false;
      const sessionDuration = isRemembered
        ? durationToMs(config.jwt.rememberExpiresIn)  // From .env: JWT_REMEMBER_EXPIRES_IN
        : durationToMs(config.jwt.accessExpiresIn); // 24h standard sessions

      const sessionExpiresAt = new Date(Date.now() + sessionDuration);

      const session = await this.prisma.session.create({
        data: {
          userId: user.id,
          token: '', // Will be updated with actual token
          expiresAt: sessionExpiresAt,
          ipAddress,
          userAgent,
        },
      });

      // Generate tokens with new organization-role structure
      const accessToken = JWTService.generateAccessToken({
        userId: user.id,
        email: user.email,
        organizationIds: (user as any).organizationIds || [],
        organizationsRoles: (user as any).organizationsRoles || [],
        sessionId: session.id,
      });

      // Generate refresh token with duration based on isRemembered
      const refreshTokenExpiresIn = isRemembered
        ? config.jwt.rememberExpiresIn  // Da .env: JWT_REMEMBER_EXPIRES_IN (default: 5d)
        : config.jwt.accessExpiresIn;   // Da .env: JWT_ACCESS_EXPIRES_IN (default: 15m)

      const refreshToken = JWTService.generateRefreshToken({
        userId: user.id,
        email: user.email,
        organizationIds: (user as any).organizationIds || [],
        organizationsRoles: (user as any).organizationsRoles || [],
        sessionId: session.id,
      }, refreshTokenExpiresIn);

      // Update session with token
      await this.prisma.session.update({
        where: { id: session.id },
        data: { token: accessToken },
      });

      // Store refresh token
      const refreshExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
      await this.prisma.refreshToken.create({
        data: {
          userId: user.id,
          token: refreshToken,
          expiresAt: refreshExpiresAt,
          sessionId: session.id,
        },
      });

      // Update last login
      await this.prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });

      // Get complete raw user data (everything except password)
      const { password, ...userWithoutPassword } = user;

      // Store complete session data in Redis (with raw user data)
      const now = new Date();
      const sessionData: SessionData = {
        sessionId: session.id,
        sessionCreatedAt: now, // When the session was originally created (UTC)
        sessionExpiredAt: sessionExpiresAt, // When the session expires (same as DB)
        ipAddress: ipAddress || '',
        userAgent: userAgent || '',
        isRemembered: isRemembered,
        createdAt: now, // When this cache entry was created
        lastActivity: now,
        user: userWithoutPassword, // Raw user data without password
      };

      await redisService.storeSession(
        session.id,
        sessionData,
        Math.floor((sessionExpiresAt.getTime() - Date.now()) / 1000)
      );

      const tokenExpiration = JWTService.getTokenExpiration(accessToken);

      return createSuccessResponse(
        'Login successful',
        '200',
        {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName || undefined,
            lastName: user.lastName || undefined,
            isEmailVerified: user.isEmailVerified,
            lastLogin: new Date(),
          },
          accessToken,
          refreshToken,
          expiresAt: tokenExpiration || undefined,
          expiresIn: tokenExpiration ? Math.floor((tokenExpiration.getTime() - Date.now()) / 1000) : undefined,
          isRemembered: isRemembered,
        }
      );
    } catch (error) {
      console.error('Login error:', error);
      return createErrorResponse('Login failed. Please try again.', '400');
    }
  }

  async logout(sessionId: string, accessToken: string): Promise<AuthResponse> {
    try {
      // Blacklist access token
      await JWTService.blacklistToken(accessToken);

      // Get and blacklist refresh token
      const refreshTokenRecord = await this.prisma.refreshToken.findFirst({
        where: { sessionId, isActive: true },
      });

      if (refreshTokenRecord) {
        await JWTService.blacklistToken(refreshTokenRecord.token);
        await this.prisma.refreshToken.update({
          where: { id: refreshTokenRecord.id },
          data: { isActive: false },
        });
      }

      // Deactivate session
      await this.prisma.session.update({
        where: { id: sessionId },
        data: { isActive: false },
      });

      // Remove session from Redis (contains all user data)
      await redisService.deleteSession(sessionId);

      return createSuccessResponse(
        'Logout successful',
        '200',
        {
          logout: "ok"
        }
      );
    } catch (error) {
      console.error('Logout error:', error);
      return createErrorResponse('Logout failed. Please try again.', '400');
    }
  }

  async refreshTokens(refreshToken: string, accessToken?: string): Promise<AuthResponse> {
    try {
      // Verify refresh token
      const verification = await JWTService.verifyRefreshToken(refreshToken);
      if (!verification.isValid || !verification.payload) {
        return createErrorResponse('Invalid refresh token', '401');
      }

      const payload = verification.payload;

      // 1. Check redis session
      const sessionData = await redisService.getSession(payload.sessionId);
      if (!sessionData || !sessionData.user) {
        return createErrorResponse('Logout required', '401');
      }

      const isRemembered = sessionData.isRemembered || false;
      const now = new Date();

      // 2. Controlli di scadenza usando sessionExpiredAt dalla cache
      const sessionExpiredAt = new Date(sessionData.sessionExpiredAt);

      if (isRemembered) {
        // Se isRemembered = true: controllare sessionExpiredAt (basato su rememberExpiresIn)
        if (now >= sessionExpiredAt) {
          return createErrorResponse('Logout required', '401');
        }
      } else {
        // Se isRemembered = false: controllare sia sessionExpiredAt che access token
        if (now >= sessionExpiredAt) {
          return createErrorResponse('Logout required', '401');
        }

        if (!accessToken) {
          return createErrorResponse('Access token is required when not remembered', '400');
        }

        try {
          const accessVerification = await JWTService.verifyAccessToken(accessToken);
          if (!accessVerification.isValid || accessVerification.isExpired) {
            return createErrorResponse('Logout required', '401');
          }
        } catch (error) {
          return createErrorResponse('Logout required', '401');
        }
      }

      // Check if refresh token exists and is active
      const refreshTokenRecord = await this.prisma.refreshToken.findFirst({
        where: {
          token: refreshToken,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
      });

      if (!refreshTokenRecord) {
        return createErrorResponse('Refresh token not found or expired', '400');
      }

      // Get user with organizationId and roleIds
      const user = await this.prisma.user.findUnique({
        where: { id: refreshTokenRecord.userId },
      });

      if (!user || !user.isActive) {
        return createErrorResponse('User not found or inactive', '400');
      }

      // Generate new tokens with organization-role structure
      const newAccessToken = JWTService.generateAccessToken({
        userId: payload.userId,
        email: user.email,
        organizationIds: (user as any).organizationIds || [],
        organizationsRoles: (user as any).organizationsRoles || [],
        sessionId: payload.sessionId,
      });

      // sessionData and isRemembered already available from previous checks

      // Generate refresh token with duration based on isRemembered
      const refreshTokenExpiresIn = isRemembered
        ? config.jwt.rememberExpiresIn  // From .env: JWT_REMEMBER_EXPIRES_IN (default: 7d)
        : config.jwt.accessExpiresIn;   // From .env: JWT_ACCESS_EXPIRES_IN (default: 24h)

      const newRefreshToken = JWTService.generateRefreshToken({
        userId: payload.userId,
        email: user.email,
        organizationIds: (user as any).organizationIds || [],
        organizationsRoles: (user as any).organizationsRoles || [],
        sessionId: payload.sessionId,
      }, refreshTokenExpiresIn);

      // Blacklist old refresh token
      await JWTService.blacklistToken(refreshToken);
      await this.prisma.refreshToken.update({
        where: { id: refreshTokenRecord.id },
        data: { isActive: false },
      });

      // Store new refresh token with correct expiration from config
      const refreshDuration = isRemembered
        ? durationToMs(config.jwt.rememberExpiresIn)  // Da .env: JWT_REMEMBER_EXPIRES_IN
        : durationToMs(config.jwt.accessExpiresIn);   // Da .env: JWT_ACCESS_EXPIRES_IN

      const refreshExpiresAt = new Date(Date.now() + refreshDuration);
      await this.prisma.refreshToken.create({
        data: {
          userId: payload.userId,
          token: newRefreshToken,
          expiresAt: refreshExpiresAt,
          sessionId: payload.sessionId,
        },
      });

      // Update session
      await this.prisma.session.update({
        where: { id: payload.sessionId },
        data: { token: newAccessToken },
      });

      const tokenExpiration = JWTService.getTokenExpiration(newAccessToken);

      return createSuccessResponse(
        'Tokens refreshed successfully',
        '200',
        {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresAt: tokenExpiration || undefined,
          expiresIn: tokenExpiration ? Math.floor((tokenExpiration.getTime() - Date.now()) / 1000) : undefined,
          isRemembered: isRemembered,
        }
      );
    } catch (error) {
      console.error('Token refresh error:', error);
      return createErrorResponse('Token refresh failed', '400');
    }
  }

  async verifyEmail(token: string): Promise<AuthResponse> {
    try {
      // Verify email verification token
      const verification = await JWTService.verifyEmailVerificationToken(token);
      if (!verification.isValid || !verification.payload) {
        return createErrorResponse('Invalid or expired verification token', '400');
      }

      const payload = verification.payload;

      // Find user and verify token matches
      const user = await this.prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          emailVerificationToken: true,
          emailVerificationExpires: true,
          isEmailVerified: true,
        },
      });

      if (!user) {
        return createErrorResponse('User not found', '400');
      }

      if (user.isEmailVerified) {
        return createSuccessResponse('Email already verified', '400', {});
      }

      if (user.emailVerificationToken !== token ||
          !user.emailVerificationExpires ||
          user.emailVerificationExpires < new Date()) {
        return createErrorResponse('Invalid or expired verification token', '400');
      }

      // Update user as verified
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          isEmailVerified: true,
          emailVerificationToken: null,
          emailVerificationExpires: null,
        },
      });

      return createSuccessResponse('Email verified successfully', '200', {});
    } catch (error) {
      console.error('Email verification error:', error);
      return createErrorResponse('Email verification failed', '400');
    }
  }
}

export const authService = new AuthService();
export default authService;
