import Fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import cookie from '@fastify/cookie';
import multipart from '@fastify/multipart';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { config } from '@/config/environment';
import { databaseService } from '@/config/database';
import { redisService } from '@/config/redis';

export async function createServer(): Promise<FastifyInstance> {
  const server = Fastify({
    logger: {
      level: config.server.nodeEnv === 'development' ? 'info' : 'warn',
      transport: config.server.nodeEnv === 'development' ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss UTC',
          ignore: 'pid,hostname',
        },
      } : undefined,
    },
    trustProxy: true,
    bodyLimit: 10 * 1024 * 1024, // 10MB
  });

  // Register CORS
  await server.register(cors, {
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);

      // Check if origin matches allowed patterns
      const isAllowed = config.cors.origins.some(allowedOrigin => {
        // Handle wildcard subdomains (*.domain.com)
        if (allowedOrigin.includes('*')) {
          const pattern = allowedOrigin.replace('*', '.*');
          const regex = new RegExp(`^${pattern}$`);
          return regex.test(origin);
        }
        // Exact match
        return allowedOrigin === origin;
      });

      if (isAllowed) {
        return callback(null, true);
      }

      return callback(new Error('Not allowed by CORS'), false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });

  // Register cookie support
  await server.register(cookie, {
    secret: config.cookie.secret,
    parseOptions: {
      httpOnly: true,
      secure: config.cookie.secure,
      sameSite: config.cookie.sameSite,
      domain: config.cookie.domain,
      path: '/',
    },
  });

  // Register multipart support
  await server.register(multipart, {
    limits: {
      fieldNameSize: 100,
      fieldSize: 100,
      fields: 10,
      fileSize: 5 * 1024 * 1024, // 5MB
      files: 1,
      headerPairs: 2000,
    },
  });

  // Register Swagger (only if enabled)
  if (config.swagger.enabled) {
    await server.register(swagger, {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'API Gateway Services',
          description: 'Complete TypeScript backend with advanced authentication system',
          version: '1.0.0',
          contact: {
            name: 'Dale Costa',
            email: '<EMAIL>',
          },
        },
        servers: [
          {
            url: `http://${config.swagger.host}`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
    });

    await server.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
    });
  }

  // Add request ID and client IP
  server.addHook('onRequest', async (request, reply) => {
    // Generate request ID
    request.id = request.headers['x-request-id'] as string || 
                 `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Get client IP
    request.clientIp = request.headers['x-forwarded-for'] as string ||
                       request.headers['x-real-ip'] as string ||
                       request.ip;
    
    // Add request ID to response headers
    reply.header('X-Request-ID', request.id);
  });

  // Global error handler
  server.setErrorHandler(async (error, request, reply) => {
    const requestId = request.id;
    
    // Log error
    server.log.error({
      error: error.message,
      stack: error.stack,
      requestId,
      url: request.url,
      method: request.method,
      ip: request.clientIp,
    }, 'Request error');

    // Handle validation errors
    if (error.validation) {
      return reply.status(400).send({
        status: false,
        message: 'Validation error: ' + error.message,
        code: '400',
        data: null,
      });
    }

    // Handle known errors
    if (error.statusCode) {
      return reply.status(error.statusCode).send({
        status: false,
        message: 'general error',
        code: '400',
        data: null,
      });
    }

    // Handle unknown errors
    return reply.status(500).send({
        status: false,
        message: 'Internal error',
        code: '500',
        data: null,
      });
  });

  // Health check endpoint
  server.get('/health', async (request, reply) => {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.server.nodeEnv,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: databaseService.isClientConnected(),
        redis: redisService.isClientConnected(),
      },
    };

    // Check database health
    try {
      const dbHealth = await databaseService.healthCheck();
      health.services.database = dbHealth;
    } catch (error) {
      health.services.database = false;
    }

    const allServicesHealthy = Object.values(health.services).every(service => service === true);
    const statusCode = allServicesHealthy ? 200 : 503;

    return reply.status(statusCode).send(health);
  });

  // Ready check endpoint
  server.get('/ready', async (request, reply) => {
    const isReady = databaseService.isClientConnected() && redisService.isClientConnected();
    
    if (isReady) {
      return reply.send({ status: 'ready' });
    } else {
      return reply.status(503).send({ status: 'not ready' });
    }
  });

  return server;
}

// Declare additional properties in FastifyRequest
declare module 'fastify' {
  interface FastifyRequest {
    id: string;
    clientIp: string;
  }
}

export default createServer;
