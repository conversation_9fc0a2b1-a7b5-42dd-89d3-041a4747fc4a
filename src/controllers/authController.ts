import { FastifyRequest, FastifyReply } from 'fastify';
import { authService } from '@/services/authService';
import { JWTService } from '@/utils/jwt';
import { LoginRequest, RegisterRequest, ValidateTokenRequest, RefreshTokenRequest, AuthResponse } from '@/types/auth';
import { AuthenticatedRequest } from '@/types/fastify';
import { config } from '@/config/environment';

// Helper function to convert duration string to milliseconds
function durationToMs(duration: string): number {
  const match = duration.match(/^(\d+)([smhd])$/);
  if (!match) return 0;

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 's': return value * 1000;
    case 'm': return value * 60 * 1000;
    case 'h': return value * 60 * 60 * 1000;
    case 'd': return value * 24 * 60 * 60 * 1000;
    default: return 0;
  }
}

export class AuthController {
  static async register(request: FastifyRequest, reply: FastifyReply) {
    try {
      const data = request.body as RegisterRequest;
      const ipAddress = request.clientIp;
      const userAgent = request.headers['user-agent'];

      const result = await authService.register(data);

      return reply.status(result.status ? 201 : 400).send(result);
    } catch (error) {
      console.error('Register controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async login(request: FastifyRequest, reply: FastifyReply) {
    try {
      const data = request.body as LoginRequest;
      const ipAddress = request.clientIp;
      const userAgent = request.headers['user-agent'];

      const result = await authService.login(data, ipAddress, userAgent);

      if (result.status && result.data?.accessToken && result.data?.refreshToken) {
        // Set secure cookies
        const cookieOptions = {
          httpOnly: true,
          secure: config.cookie.secure,
          sameSite: config.cookie.sameSite as 'strict' | 'lax' | 'none',
          domain: config.cookie.domain,
          path: '/',
        };

        // Set access token cookie with duration from config
        const accessTokenExpires = result.data.expiresAt ||
          new Date(Date.now() + durationToMs(config.jwt.accessExpiresIn));
        reply.setCookie('accessToken', result.data.accessToken, {
          ...cookieOptions,
          expires: accessTokenExpires,
        });

        // Set refresh token cookie with duration based on isRemembered from config
        const isRemembered = data.rememberMe || false;
        const refreshTokenExpires = isRemembered
          ? new Date(Date.now() + durationToMs(config.jwt.rememberExpiresIn)) // Da .env: JWT_REMEMBER_EXPIRES_IN
          : accessTokenExpires; // Stessa durata dell'access token se non "ricordami"

        reply.setCookie('refreshToken', result.data.refreshToken, {
          ...cookieOptions,
          expires: refreshTokenExpires,
        });

        // Set isRemembered status with duration from config
        reply.setCookie('isRemembered', isRemembered ? 'true' : 'false', {
          ...cookieOptions,
          expires: isRemembered
            ? new Date(Date.now() + durationToMs(config.jwt.rememberExpiresIn)) // Da .env: JWT_REMEMBER_EXPIRES_IN
            : new Date(Date.now() + durationToMs('1d')), // 1 giorno se non "ricordami"
        });
      }

      return reply.status(result.status ? 200 : 401).send(result);
    } catch (error) {
      console.error('Login controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async logout(request: AuthenticatedRequest, reply: FastifyReply) {
    try {
      const sessionId = request.session.id;
      const accessToken = request.cookies.accessToken || 
                         (request.headers.authorization?.startsWith('Bearer ') ? 
                          request.headers.authorization.substring(7) : '');

      const result = await authService.logout(sessionId, accessToken);

      // Clear cookies
      reply.clearCookie('accessToken', {
        domain: config.cookie.domain,
        path: '/',
      });
      reply.clearCookie('refreshToken', {
        domain: config.cookie.domain,
        path: '/',
      });
      reply.clearCookie('isRemembered', {
        domain: config.cookie.domain,
        path: '/',
      });

      return reply.status(result.status ? 200 : 400).send(result);
    } catch (error) {
      console.error('Logout controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async validateToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { token } = request.body as ValidateTokenRequest;

      const validation = await JWTService.verifyAccessToken(token);

      const response: AuthResponse = {
        status: validation.isValid,
        code: validation.isValid ? '200' : '401',
        message: validation.isValid ? 'Token is valid' :
              validation.isBlacklisted ? 'Token revoked' :
              validation.isExpired ? 'Token expired' : 'Invalid token',
        data: validation.isValid ? {
          expiresAt: validation.expiresAt,
          expiresIn: validation.expiresIn,
        } : null,
      };

      return reply.status(validation.isValid ? 200 : 401).send(response);
    } catch (error) {
      console.error('Validate token controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async refreshToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Get tokens from body or cookies
      const bodyData = request.body as RefreshTokenRequest;
      const refreshToken = bodyData.refreshToken || request.cookies.refreshToken;
      const accessToken = bodyData.accessToken || request.cookies.accessToken;

      if (!refreshToken) {
        return reply.status(401).send({
          status: false,
          message: 'Refresh token is required',
          code: '401',
          data: null,
        });
      }

      const result = await authService.refreshTokens(refreshToken, accessToken);

      if (result.status && result.data?.accessToken && result.data?.refreshToken) {
        // Get current isRemembered status from cookies
        const currentIsRemembered = request.cookies.isRemembered === 'true';

        // Update cookies with new tokens
        const cookieOptions = {
          httpOnly: true,
          secure: config.cookie.secure,
          sameSite: config.cookie.sameSite as 'strict' | 'lax' | 'none',
          domain: config.cookie.domain,
          path: '/',
        };

        // Set new access token cookie with duration from config
        const accessTokenExpires = result.data.expiresAt ||
          new Date(Date.now() + durationToMs(config.jwt.accessExpiresIn));
        reply.setCookie('accessToken', result.data.accessToken, {
          ...cookieOptions,
          expires: accessTokenExpires,
        });

        // Set new refresh token cookie with duration based on isRemembered from config
        const refreshTokenExpires = currentIsRemembered
          ? new Date(Date.now() + durationToMs(config.jwt.rememberExpiresIn)) // Da .env: JWT_REMEMBER_EXPIRES_IN
          : accessTokenExpires; // Stessa durata dell'access token se non "ricordami"

        reply.setCookie('refreshToken', result.data.refreshToken, {
          ...cookieOptions,
          expires: refreshTokenExpires,
        });

        // Preserve isRemembered status with duration from config
        reply.setCookie('isRemembered', currentIsRemembered ? 'true' : 'false', {
          ...cookieOptions,
          expires: currentIsRemembered
            ? new Date(Date.now() + durationToMs(config.jwt.rememberExpiresIn)) // Da .env: JWT_REMEMBER_EXPIRES_IN
            : new Date(Date.now() + durationToMs('1d')), // 1 giorno se non "ricordami"
        });
      }

      return reply.status(result.status ? 200 : 401).send(result);
    } catch (error) {
      console.error('Refresh token controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }



  static async verifyEmailAPI(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { token } = request.body as { token: string };

      const result = await authService.verifyEmail(token);

      return reply.status(result.status ? 200 : 400).send(result);
    } catch (error) {
      console.error('Verify email API controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async getProfile(request: AuthenticatedRequest, reply: FastifyReply) {
    try {
      const userId = request.user.id;

      // Get user profile from database
      const { databaseService } = await import('@/config/database');
      const prisma = databaseService.getClient();
      
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          isEmailVerified: true,
          isActive: true,
          lastLogin: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        return reply.status(404).send({
          status: false,
          message: 'User not found',
          code: '400',
          data: null,
        });
      }

      return reply.send({
        status: true,
        message: 'Profile retrieved successfully',
        code: '200',
        data: { user },
      });
    } catch (error) {
      console.error('Get profile controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }

  static async getCurrentSessions(request: AuthenticatedRequest, reply: FastifyReply) {
    try {
      const userId = request.user.id;

      const { databaseService } = await import('@/config/database');
      const prisma = databaseService.getClient();
      
      const sessions = await prisma.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        select: {
          id: true,
          ipAddress: true,
          userAgent: true,
          createdAt: true,
          expiresAt: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      return reply.send({
        status: true,
        message: 'Sessions retrieved successfully',
        code: '200',
        data: { sessions },
      });
    } catch (error) {
      console.error('Get sessions controller error:', error);
      return reply.status(500).send({
        status: false,
        message: 'Internal server error',
        code: '500',
        data: null,
      });
    }
  }
}

export default AuthController;
