import { createClient, RedisClientType } from 'redis';
import { config } from './environment';

class RedisService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient({
      url: config.redis.url,
      password: config.redis.password || undefined,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500),
      },
    });

    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
      this.isConnected = false;
    });

    this.client.on('connect', () => {
      console.log('Redis Client Connected');
      this.isConnected = true;
    });

    this.client.on('disconnect', () => {
      console.log('Redis Client Disconnected');
      this.isConnected = false;
    });
  }

  async connect(): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect();
      }
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.client.disconnect();
      }
    } catch (error) {
      console.error('Failed to disconnect from Redis:', error);
      throw error;
    }
  }

  // Token blacklist methods
  async blacklistToken(token: string, expiresIn: number): Promise<void> {
    try {
      await this.client.setEx(`blacklist:${token}`, expiresIn, 'true');
    } catch (error) {
      console.error('Failed to blacklist token:', error);
      throw error;
    }
  }

  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const result = await this.client.get(`blacklist:${token}`);
      return result === 'true';
    } catch (error) {
      console.error('Failed to check token blacklist:', error);
      return false;
    }
  }

  // Session management
  async storeSession(sessionId: string, data: any, expiresIn: number): Promise<void> {
    try {
      await this.client.setEx(`session:${sessionId}`, expiresIn, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to store session:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<any | null> {
    try {
      const result = await this.client.get(`session:${sessionId}`);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      console.error('Failed to get session:', error);
      return null;
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.client.del(`session:${sessionId}`);
    } catch (error) {
      console.error('Failed to delete session:', error);
      throw error;
    }
  }

  // User cache management
  async storeUserCache(sessionId: string, userData: any, expiresIn: number): Promise<void> {
    try {
      await this.client.setEx(`user_cache:${sessionId}`, expiresIn, JSON.stringify(userData));
    } catch (error) {
      console.error('Failed to store user cache:', error);
      throw error;
    }
  }

  async getUserCache(sessionId: string): Promise<any | null> {
    try {
      const result = await this.client.get(`user_cache:${sessionId}`);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      console.error('Failed to get user cache:', error);
      return null;
    }
  }

  async deleteUserCache(sessionId: string): Promise<void> {
    try {
      await this.client.del(`user_cache:${sessionId}`);
    } catch (error) {
      console.error('Failed to delete user cache:', error);
      throw error;
    }
  }

  // User session tracking (per cleanup multipli)
  async addUserSession(userId: string, sessionId: string): Promise<void> {
    try {
      await this.client.sAdd(`user_sessions:${userId}`, sessionId);
    } catch (error) {
      console.error('Failed to add user session:', error);
      throw error;
    }
  }

  async removeUserSession(userId: string, sessionId: string): Promise<void> {
    try {
      await this.client.sRem(`user_sessions:${userId}`, sessionId);
    } catch (error) {
      console.error('Failed to remove user session:', error);
      throw error;
    }
  }

  async getUserSessions(userId: string): Promise<string[]> {
    try {
      return await this.client.sMembers(`user_sessions:${userId}`);
    } catch (error) {
      console.error('Failed to get user sessions:', error);
      return [];
    }
  }

  async clearAllUserSessions(userId: string): Promise<void> {
    try {
      const sessions = await this.getUserSessions(userId);
      const pipeline = this.client.multi();

      // Delete all user caches for this user
      sessions.forEach(sessionId => {
        pipeline.del(`user_cache:${sessionId}`);
        pipeline.del(`session:${sessionId}`);
      });

      // Delete the user sessions set
      pipeline.del(`user_sessions:${userId}`);

      await pipeline.exec();
    } catch (error) {
      console.error('Failed to clear all user sessions:', error);
      throw error;
    }
  }

  // Rate limiting
  async incrementRateLimit(key: string, windowMs: number): Promise<number> {
    try {
      const current = await this.client.incr(`rate_limit:${key}`);
      if (current === 1) {
        await this.client.expire(`rate_limit:${key}`, Math.ceil(windowMs / 1000));
      }
      return current;
    } catch (error) {
      console.error('Failed to increment rate limit:', error);
      throw error;
    }
  }

  // Generic cache methods
  async set(key: string, value: string, expiresIn?: number): Promise<void> {
    try {
      if (expiresIn) {
        await this.client.setEx(key, expiresIn, value);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      console.error('Failed to set cache:', error);
      throw error;
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error) {
      console.error('Failed to get cache:', error);
      return null;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      console.error('Failed to delete cache:', error);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Failed to check key existence:', error);
      return false;
    }
  }

  // Helper method to get session age in milliseconds
  async getSessionAge(sessionId: string): Promise<number | null> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (sessionData && sessionData.sessionCreatedAt) {
        return Date.now() - new Date(sessionData.sessionCreatedAt).getTime();
      }
      return null;
    } catch (error) {
      console.error('Failed to get session age:', error);
      return null;
    }
  }

  // Helper method to check if session should be renewed (e.g., after 7 days)
  async shouldRenewSession(sessionId: string, maxAgeMs: number = 7 * 24 * 60 * 60 * 1000): Promise<boolean> {
    try {
      const sessionAge = await this.getSessionAge(sessionId);
      return sessionAge !== null && sessionAge > maxAgeMs;
    } catch (error) {
      console.error('Failed to check session renewal:', error);
      return false;
    }
  }

  getClient(): RedisClientType {
    return this.client;
  }

  isClientConnected(): boolean {
    return this.isConnected;
  }
}

export const redisService = new RedisService();
export default redisService;
