import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface Config {
  server: {
    port: number;
    host: string;
    nodeEnv: string;
  };
  database: {
    url: string;
    autoCreate: boolean;
  };
  redis: {
    url: string;
    password?: string;
  };
  jwt: {
    secret: string;
    refreshSecret: string;
    accessExpiresIn: string;
    refreshExpiresIn: string;
    rememberExpiresIn: string;
  };
  cookie: {
    secret: string;
    domain: string;
    allowedDomains: string[];
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
  };
  email: {
    smtp: {
      host: string;
      port: number;
      user: string;
      pass: string;
    };
    from: string;
  };
  app: {
    frontendUrl: string;
    backendUrl: string;
  };
  cors: {
    origins: string[];
  };
  swagger: {
    enabled: boolean;
    host: string;
  };
  security: {
    bcryptRounds: number;
    rateLimitMax: number;
    rateLimitWindowMs: number;
  };
  verification: {
    emailExpiresIn: string;
  };
  roles: {
    adminId: string;
    readId: string;
    writeId: string;
    deleteId: string;
  };
}

const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'COOKIE_SECRET',
];

// Validate required environment variables
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export const config: Config = {
  server: {
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || 'localhost',
    nodeEnv: process.env.NODE_ENV || 'development',
  },
  database: {
    url: process.env.DATABASE_URL!,
    autoCreate: process.env.AUTO_CREATE_DATABASE === 'true',
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
  },
  jwt: {
    secret: process.env.JWT_SECRET!,
    refreshSecret: process.env.JWT_REFRESH_SECRET!,
    accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    rememberExpiresIn: process.env.JWT_REMEMBER_EXPIRES_IN || '5d',
  },
  cookie: {
    secret: process.env.COOKIE_SECRET!,
    domain: process.env.COOKIE_DOMAIN || 'localhost',
    allowedDomains: process.env.COOKIE_ALLOWED_DOMAINS
      ? process.env.COOKIE_ALLOWED_DOMAINS.split(',').map(d => d.trim())
      : [process.env.COOKIE_DOMAIN || 'localhost'],
    secure: process.env.COOKIE_SECURE === 'true',
    sameSite: (process.env.COOKIE_SAME_SITE as 'strict' | 'lax' | 'none') || 'lax',
  },
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || '',
    },
    from: process.env.EMAIL_FROM || '<EMAIL>',
  },
  app: {
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3001',
    backendUrl: process.env.BACKEND_URL || 'http://localhost:3000',
  },
  cors: {
    origins: process.env.CORS_ORIGINS
      ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
      : ['http://localhost:3000', 'http://localhost:3001'],
  },
  swagger: {
    enabled: process.env.ENABLE_SWAGGER === 'true',
    host: process.env.SWAGGER_HOST || 'localhost:8080',
  },

  // Role IDs
  roles: {
    adminId: process.env.ROLE_ADMIN_ID || '',
    readId: process.env.ROLE_READ_ID || '',
    writeId: process.env.ROLE_WRITE_ID || '',
    deleteId: process.env.ROLE_DELETE_ID || '',
  },
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
  },
  verification: {
    emailExpiresIn: process.env.EMAIL_VERIFICATION_EXPIRES_IN || '24h',
  },
};

export default config;
