import { PrismaClient } from '@prisma/client';
import { config } from './environment';
import { DatabaseInitService } from '@/services/databaseInitService';

class DatabaseService {
  private prisma: PrismaClient;
  private isConnected: boolean = false;
  private initService: DatabaseInitService;

  constructor() {
    this.prisma = new PrismaClient({
      log: config.server.nodeEnv === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
    this.initService = new DatabaseInitService(this.prisma);
    this.initService = new DatabaseInitService(this.prisma);

    // MongoDB doesn't support all Prisma events
    // this.prisma.$on('beforeExit', async () => {
    //   console.log('Prisma is disconnecting...');
    //   this.isConnected = false;
    // });
  }

  async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.isConnected = true;
      console.log('✅ Database connected successfully');

      // Initialize database structure if enabled
      if (config.database.autoCreate) {
        await this.initService.initializeDatabase();
      }
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.isConnected = false;
      console.log('Database disconnected successfully');
    } catch (error) {
      console.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  async initializeDatabase(): Promise<void> {
    try {
      console.log('Initializing database...');
      
      // Check if database is accessible
      await this.connect();
      
      // Create indexes if they don't exist
      await this.createIndexes();
      
      console.log('Database initialization completed');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createIndexes(): Promise<void> {
    try {
      // Note: MongoDB with Prisma automatically creates indexes based on the schema
      // But we can add custom indexes here if needed
      
      console.log('Checking and creating database indexes...');
      
      // Example of creating a custom index (if needed)
      // await this.prisma.$runCommandRaw({
      //   createIndexes: 'users',
      //   indexes: [
      //     {
      //       key: { email: 1 },
      //       name: 'email_unique_index',
      //       unique: true
      //     }
      //   ]
      // });
      
      console.log('Database indexes verified');
    } catch (error) {
      console.error('Failed to create indexes:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Simple connection test for MongoDB
      await this.prisma.user.findFirst({
        select: { id: true }
      });
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  getClient(): PrismaClient {
    return this.prisma;
  }

  isClientConnected(): boolean {
    return this.isConnected;
  }

  // Utility method to handle database transactions
  async transaction<T>(fn: (prisma: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>): Promise<T> {
    return await this.prisma.$transaction(fn);
  }

  // Graceful shutdown
  async gracefulShutdown(): Promise<void> {
    console.log('Initiating graceful database shutdown...');
    try {
      await this.disconnect();
      console.log('Database shutdown completed');
    } catch (error) {
      console.error('Error during database shutdown:', error);
      throw error;
    }
  }
}

export const databaseService = new DatabaseService();
export default databaseService;
