import jwt from 'jsonwebtoken';
import { config } from '@/config/environment';
import { TokenPayload, RefreshTokenPayload, EmailVerificationPayload, TokenValidationResult } from '@/types/auth';
import { redisService } from '@/config/redis';

export class JWTService {
  private static parseTimeToSeconds(time: string): number {
    const unit = time.slice(-1);
    const value = parseInt(time.slice(0, -1));
    
    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return value;
    }
  }

  static generateAccessToken(payload: Omit<TokenPayload, 'type' | 'iat' | 'exp'>): string {
    // Access token SEMPRE usa JWT_ACCESS_EXPIRES_IN, indipendente da isRemembered
    const expiresIn = config.jwt.accessExpiresIn;
    
    const tokenPayload: Omit<TokenPayload, 'iat' | 'exp'> = {
      ...payload,
      type: 'access',
    };

    return jwt.sign(tokenPayload, config.jwt.secret, {
      expiresIn,
      issuer: 'api-gateway-services.dalecosta.com',
      audience: 'dale-api-gateway-client',
    } as jwt.SignOptions);
  }

  static generateRefreshToken(
    payload: Omit<RefreshTokenPayload, 'type' | 'iat' | 'exp'>,
    customExpiresIn?: string
  ): string {
    const tokenPayload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {
      ...payload,
      type: 'refresh',
    };

    return jwt.sign(tokenPayload, config.jwt.refreshSecret, {
      expiresIn: customExpiresIn || config.jwt.refreshExpiresIn,
      issuer: 'api-gateway-services.dalecosta.com',
      audience: 'dale-api-gateway-client',
    } as jwt.SignOptions);
  }

  static generateEmailVerificationToken(payload: Omit<EmailVerificationPayload, 'type' | 'iat' | 'exp'>): string {
    const tokenPayload: Omit<EmailVerificationPayload, 'iat' | 'exp'> = {
      ...payload,
      type: 'email_verification',
    };

    return jwt.sign(tokenPayload, config.jwt.secret, {
      expiresIn: config.verification.emailExpiresIn,
      issuer: 'api-gateway-services.dalecosta.com',
      audience: 'dale-api-gateway-client',
    } as jwt.SignOptions);
  }

  static async verifyAccessToken(token: string): Promise<TokenValidationResult> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await redisService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        return {
          isValid: false,
          isExpired: false,
          isBlacklisted: true,
        };
      }

      const payload = jwt.verify(token, config.jwt.secret, {
        issuer: 'api-gateway-services.dalecosta.com',
        audience: 'dale-api-gateway-client',
      }) as TokenPayload;

      if (payload.type !== 'access') {
        return {
          isValid: false,
          isExpired: false,
          isBlacklisted: false,
        };
      }

      const expiresAt = new Date((payload.exp || 0) * 1000);
      const expiresIn = Math.max(0, Math.floor((expiresAt.getTime() - Date.now()) / 1000));

      return {
        isValid: true,
        isExpired: false,
        isBlacklisted: false,
        payload,
        expiresAt,
        expiresIn,
      };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return {
          isValid: false,
          isExpired: true,
          isBlacklisted: false,
        };
      }

      return {
        isValid: false,
        isExpired: false,
        isBlacklisted: false,
      };
    }
  }

  static async verifyRefreshToken(token: string): Promise<{ isValid: boolean; payload?: RefreshTokenPayload }> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await redisService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        return { isValid: false };
      }

      const payload = jwt.verify(token, config.jwt.refreshSecret, {
        issuer: 'api-gateway-services.dalecosta.com',
        audience: 'dale-api-gateway-client',
      }) as RefreshTokenPayload;

      if (payload.type !== 'refresh') {
        return { isValid: false };
      }

      return { isValid: true, payload };
    } catch (error) {
      return { isValid: false };
    }
  }

  static async verifyEmailVerificationToken(token: string): Promise<{ isValid: boolean; payload?: EmailVerificationPayload }> {
    try {
      const payload = jwt.verify(token, config.jwt.secret, {
        issuer: 'api-gateway-services.dalecosta.com',
        audience: 'dale-api-gateway-client',
      }) as EmailVerificationPayload;

      if (payload.type !== 'email_verification') {
        return { isValid: false };
      }

      return { isValid: true, payload };
    } catch (error) {
      return { isValid: false };
    }
  }

  static async blacklistToken(token: string): Promise<void> {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        const expiresIn = Math.max(0, decoded.exp - Math.floor(Date.now() / 1000));
        if (expiresIn > 0) {
          await redisService.blacklistToken(token, expiresIn);
        }
      }
    } catch (error) {
      console.error('Failed to blacklist token:', error);
      // Still try to blacklist with a default expiration
      await redisService.blacklistToken(token, this.parseTimeToSeconds(config.jwt.accessExpiresIn));
    }
  }

  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  static getTokenPayload(token: string): any | null {
    try {
      return jwt.decode(token);
    } catch (error) {
      return null;
    }
  }
}

export default JWTService;
