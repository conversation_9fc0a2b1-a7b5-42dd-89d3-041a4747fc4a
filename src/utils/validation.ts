import { z } from 'zod';

// Email validation schema
export const emailSchema = z
  .string()
  .email('Invalid email format')
  .min(1, 'Email is required')
  .max(255, 'Email is too long')
  .transform(email => email.toLowerCase().trim());

// Password validation schema
export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must be less than 128 characters long')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, 'Password must contain at least one special character');

// Name validation schema
export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(50, 'Name is too long')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
  .transform(name => name.trim());

// Optional name schema
export const optionalNameSchema = nameSchema.optional();

// Address schema
export const addressSchema = z.object({
  street: z.string().min(1, 'Street is required').max(100, 'Street must be less than 100 characters'),
  city: z.string().min(1, 'City is required').max(50, 'City must be less than 50 characters'),
  postalCode: z.string().min(1, 'Postal code is required').max(20, 'Postal code must be less than 20 characters'),
  country: z.string().min(1, 'Country is required').max(50, 'Country must be less than 50 characters'),
});

// Organization schema
export const organizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required').max(100, 'Organization name must be less than 100 characters'),
  vatNumber: z.string().min(1, 'VAT number is required').max(50, 'VAT number must be less than 50 characters'),
});

// Phone number schema
export const phoneSchema = z.string()
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
  .optional();

// Register request schema
export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  phoneNumber: phoneSchema,
  address: addressSchema,
  organization: organizationSchema,
  roleIds: z.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format')).optional(), // Optional array of role IDs
});

// Login request schema
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false),
});

// Token validation schema
export const validateTokenSchema = z.object({
  token: z.string().min(1, 'Token is required'),
});

// Refresh token schema
export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

// Email verification schema
export const emailVerificationSchema = z.object({
  token: z.string().min(1, 'Verification token is required'),
});

// Password reset request schema
export const passwordResetRequestSchema = z.object({
  email: emailSchema,
});

// Password reset schema
export const passwordResetSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
});

// Change password schema
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
});

// Update profile schema
export const updateProfileSchema = z.object({
  firstName: optionalNameSchema,
  lastName: optionalNameSchema,
});

// Pagination schema
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Query params schema
export const queryParamsSchema = z.object({
  search: z.string().optional(),
  filter: z.string().optional(),
});

// Generic ID schema
export const idSchema = z.string().min(1, 'ID is required');

// UUID schema (for MongoDB ObjectId)
export const objectIdSchema = z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format');

// Validation helper functions
export function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return async (request: any, reply: any) => {
    const validation = validateRequest(schema, request.body);
    
    if (!validation.success) {
      return reply.status(400).send({
        status: false,
        message: 'Validation failed: ' + validation.errors,
        code: '400',
        data: null,
      });
    }
    
    // Replace request body with validated data
    request.body = validation.data;
  };
}

// Export all schemas
export const schemas = {
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema,
  optionalName: optionalNameSchema,
  phone: phoneSchema,
  address: addressSchema,
  organization: organizationSchema,
  register: registerSchema,
  login: loginSchema,
  validateToken: validateTokenSchema,
  refreshToken: refreshTokenSchema,
  emailVerification: emailVerificationSchema,
  passwordResetRequest: passwordResetRequestSchema,
  passwordReset: passwordResetSchema,
  changePassword: changePasswordSchema,
  updateProfile: updateProfileSchema,
  pagination: paginationSchema,
  queryParams: queryParamsSchema,
  id: idSchema,
  objectId: objectIdSchema,
};

export default schemas;
