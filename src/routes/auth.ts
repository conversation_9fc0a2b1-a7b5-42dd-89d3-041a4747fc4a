import { FastifyInstance } from 'fastify';
import { AuthController } from '@/controllers/authController';
import { authenticateToken } from '@/middleware/auth';
import { authRateLimit, registerRateLimit } from '@/middleware/rateLimit';
import { createValidationMiddleware, schemas } from '@/utils/validation';

export async function authRoutes(fastify: FastifyInstance) {
  // Register endpoint
  fastify.post('/register', {
    preHandler: [
      registerRateLimit,
      createValidationMiddleware(schemas.register),
    ],
    schema: {
      description: 'Register a new user account',
      tags: ['Authentication'],
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: 'User email address',
          },
          password: {
            type: 'string',
            minLength: 8,
            description: 'User password (min 8 characters, must contain uppercase, lowercase, number, and special character)',
          },
          firstName: {
            type: 'string',
            maxLength: 50,
            description: 'User first name (optional)',
          },
          lastName: {
            type: 'string',
            maxLength: 50,
            description: 'User last name (optional)',
          },
        },
      },
      response: {
        201: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    isEmailVerified: { type: 'boolean' },
                  },
                },
              },
            },
          },
        },
        400: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
      },
    },
  }, AuthController.register);

  // Login endpoint
  fastify.post('/login', {
    preHandler: [
      authRateLimit,
      createValidationMiddleware(schemas.login),
    ],
    schema: {
      description: 'Login with email and password',
      tags: ['Authentication'],
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: 'User email address',
          },
          password: {
            type: 'string',
            description: 'User password',
          },
          rememberMe: {
            type: 'boolean',
            default: false,
            description: 'Keep user logged in for extended period (5 days)',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    isEmailVerified: { type: 'boolean' },
                    lastLogin: { type: 'string', format: 'date-time' },
                  },
                },
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
                expiresAt: { type: 'string', format: 'date-time' },
                expiresIn: { type: 'number' },
              },
            },
          },
        },
      },
    },
  }, AuthController.login);

  // Logout endpoint
  fastify.post('/logout', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Logout and invalidate current session',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
      },
    },
  }, AuthController.logout as any);

  // Validate token endpoint
  fastify.post('/validate', {
    preHandler: [createValidationMiddleware(schemas.validateToken)],
    schema: {
      description: 'Validate an access token and return expiration info',
      tags: ['Authentication'],
      body: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            description: 'JWT access token to validate',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                expiresAt: { type: 'string', format: 'date-time' },
                expiresIn: { type: 'number' },
              },
            },
          },
        },
        401: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
      },
    },
  }, AuthController.validateToken);

  // Refresh token endpoint
  fastify.post('/refresh', {
    preHandler: [createValidationMiddleware(schemas.refreshToken.partial())],
    schema: {
      description: 'Refresh access token using refresh token. Access token required when isRemembered=false.',
      tags: ['Authentication'],
      body: {
        type: 'object',
        properties: {
          refreshToken: {
            type: 'string',
            description: 'Refresh token (optional if provided in cookies)',
          },
          accessToken: {
            type: 'string',
            description: 'Access token (required when isRemembered=false, optional if provided in cookies)',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
                expiresAt: { type: 'string', format: 'date-time' },
                expiresIn: { type: 'number' },
                isRemembered: { type: 'boolean' },
              },
            },
          },
        },
        400: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
        401: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
      },
    },
  }, AuthController.refreshToken);



  // Email verification endpoint (unified API)
  fastify.post('/verify', {
    preHandler: [createValidationMiddleware(schemas.emailVerification)],
    schema: {
      description: 'Verify user email address',
      tags: ['Authentication'],
      body: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            description: 'Email verification token',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
        400: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: { type: ['object', 'null'] },
          },
        },
      },
    },
  }, AuthController.verifyEmailAPI);

  // Get current user profile
  fastify.get('/profile', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get current user profile',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    isEmailVerified: { type: 'boolean' },
                    isActive: { type: 'boolean' },
                    lastLogin: { type: 'string', format: 'date-time' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, AuthController.getProfile as any);

  // Get current user sessions
  fastify.get('/sessions', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get current user active sessions',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                sessions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      ipAddress: { type: 'string' },
                      userAgent: { type: 'string' },
                      createdAt: { type: 'string', format: 'date-time' },
                      expiresAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, AuthController.getCurrentSessions as any);
}

export default authRoutes;
