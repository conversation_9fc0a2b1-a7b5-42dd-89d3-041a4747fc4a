import { FastifyRequest } from 'fastify';
import { TokenPayload } from './auth';

declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      id: string;
      email: string;
      sessionId: string;
    };
    session?: {
      id: string;
      userId: string;
      isRemembered: boolean;
    };
    tokenPayload?: TokenPayload;
    clientIp: string;
  }
}

export interface AuthenticatedRequest extends FastifyRequest {
  user: {
    id: string;
    email: string;
    sessionId: string;
  };
  session: {
    id: string;
    userId: string;
    isRemembered: boolean;
  };
  tokenPayload: TokenPayload;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: {
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
    version?: string;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: PaginationMeta;
}
