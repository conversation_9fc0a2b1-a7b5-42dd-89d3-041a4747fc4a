export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  organization: {
    name: string;
    vatNumber: string;
  };
  roleIds?: string[]; // Optional, defaults to admin role
}

export interface ValidateTokenRequest {
  token: string;
}

export interface RefreshTokenRequest {
  refreshToken?: string;
  accessToken?: string; // Richiesto quando isRemembered = false
}

// Standard return message format
export interface ReturnMessage<T = any> {
  status: boolean;
  message: string;
  code: string;
  data: T | null;
}

// Generic auth response - can contain any data
export interface AuthResponse extends ReturnMessage<any> {}

// Specific response types for different operations
export interface LoginResponse extends ReturnMessage<{
  user?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    isEmailVerified: boolean;
    lastLogin?: Date;
  };
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
  expiresIn?: number;
  isRemembered?: boolean;
}> {}

export interface RegisterResponse extends ReturnMessage<{
  user?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    isEmailVerified: boolean;
  };
}> {}

export interface LogoutResponse extends ReturnMessage<{}> {}

// Organization role structure
export interface OrganizationRole {
  organizationId: string;
  roleIds: string[];
}

export interface TokenPayload {
  userId: string;
  email: string;
  organizationIds: string[]; // Keep for backward compatibility
  organizationsRoles: OrganizationRole[]; // New structure
  sessionId: string;
  type: 'access' | 'refresh';
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  userId: string;
  email: string;
  organizationIds: string[]; // Keep for backward compatibility
  organizationsRoles: OrganizationRole[]; // New structure
  sessionId: string;
  type: 'refresh';
  iat?: number;
  exp?: number;
}

export interface EmailVerificationPayload {
  userId: string;
  email: string;
  type: 'email_verification';
  iat?: number;
  exp?: number;
}

export interface SessionData {
  // Session info
  sessionId: string;
  sessionCreatedAt: Date; // When the session was originally created (UTC)
  sessionExpiredAt: Date; // When the session expires (based on isRemembered)
  ipAddress?: string;
  userAgent?: string;
  isRemembered: boolean;
  createdAt: Date; // When this cache entry was created
  lastActivity: Date;

  // Complete raw user data from MongoDB (everything except password)
  user: any; // Raw user object from MongoDB without password
}

// Cache completa dell'utente per sessione
export interface UserCacheData {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  isEmailVerified: boolean;
  isActive: boolean;
  lastLogin?: Date;
  organizationIds: string[];
  organizationsRoles: OrganizationRole[];
  // Dati delle organizzazioni popolati
  organizations?: {
    id: string;
    name: string;
    vatNumber: string;
  }[];
  // Dati dei ruoli popolati
  roles?: {
    id: string;
    name: string;
    permissions: string[];
  }[];
  cachedAt: Date;
}

export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  isBlacklisted: boolean;
  payload?: TokenPayload;
  expiresAt?: Date;
  expiresIn?: number;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}
