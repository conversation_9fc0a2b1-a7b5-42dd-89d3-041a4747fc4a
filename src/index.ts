import { createServer } from './server';
import { databaseService } from './config/database';
import { redisService } from './config/redis';
import { EmailService } from './services/emailService';
import { config } from './config/environment';
import { authRoutes } from './routes/auth';

// Set timezone to UTC
process.env.TZ = 'UTC';

async function startServer() {
  let server;
  
  try {
    console.log('🚀 Starting API Gateway Services...');
    console.log(`Environment: ${config.server.nodeEnv}`);
    console.log(`Port: ${config.server.port}`);
    console.log(`Host: ${config.server.host}`);

    // Initialize database
    console.log('📊 Initializing database connection...');
    await databaseService.initializeDatabase();
    console.log('✅ Database initialized successfully');

    // Initialize Redis
    console.log('🔴 Connecting to Redis...');
    await redisService.connect();
    console.log('✅ Redis connected successfully');

    // Test email service (optional)
    console.log('📧 Testing email service...');
    const emailConnected = await EmailService.testConnection();
    if (emailConnected) {
      console.log('✅ Email service connected successfully');
    } else {
      console.log('⚠️  Email service connection failed (continuing without email)');
    }

    // Create and configure server
    console.log('🌐 Creating Fastify server...');
    server = await createServer();

    // Register routes
    console.log('🛣️  Registering routes...');
    await server.register(authRoutes, { prefix: '/auth' });

    // Add a simple root endpoint
    server.get('/', async (request, reply) => {
      return {
        success: true,
        message: 'API Gateway Services is running',
        data: {
          version: '1.0.0',
          environment: config.server.nodeEnv,
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
        },
      };
    });

    // Start server
    console.log('🎯 Starting server...');
    await server.listen({
      port: config.server.port,
      host: config.server.host,
    });

    console.log('✅ Server started successfully!');
    console.log(`🌍 Server is running at: http://${config.server.host}:${config.server.port}`);
    console.log(`📚 Health check: http://${config.server.host}:${config.server.port}/health`);
    console.log(`🔐 Auth endpoints: http://${config.server.host}:${config.server.port}/auth`);

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    
    // Cleanup on startup failure
    try {
      if (server) {
        await server.close();
      }
      await databaseService.gracefulShutdown();
      await redisService.disconnect();
    } catch (cleanupError) {
      console.error('❌ Error during cleanup:', cleanupError);
    }
    
    process.exit(1);
  }
}

// Graceful shutdown handling
async function gracefulShutdown(signal: string) {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close database connections
    console.log('📊 Closing database connections...');
    await databaseService.gracefulShutdown();
    console.log('✅ Database connections closed');

    // Close Redis connections
    console.log('🔴 Closing Redis connections...');
    await redisService.disconnect();
    console.log('✅ Redis connections closed');

    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

// Start the server
startServer().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});

export default startServer;
