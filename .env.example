# Server Configuration
PORT=3000
NODE_ENV=development
HOST=localhost

# Database Configuration
DATABASE_URL="mongodb://localhost:27017/api_gateway_db"
# For MongoDB Atlas: DATABASE_URL="mongodb+srv://username:<EMAIL>/api_gateway_db?retryWrites=true&w=majority"
AUTO_CREATE_DATABASE=true

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-jwt-key-change-this-in-production"
JWT_ACCESS_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"
JWT_REMEMBER_EXPIRES_IN="5d"

# Cookie Configuration
COOKIE_SECRET="your-super-secret-cookie-key-change-this-in-production"
COOKIE_DOMAIN="localhost"
COOKIE_ALLOWED_DOMAINS="localhost,.yourdomain.com"
COOKIE_SECURE=false
COOKIE_SAME_SITE="lax"

# Email Configuration (for user verification)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# Application URLs
FRONTEND_URL="http://localhost:3001"
BACKEND_URL="http://localhost:3000"

# CORS Configuration - Allowed Origins for API calls
CORS_ORIGINS="http://localhost:3000,http://localhost:3001,https://yourdomain.com,https://*.yourdomain.com"

# Swagger Documentation
ENABLE_SWAGGER=true
SWAGGER_HOST="localhost:3000"

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# Verification
EMAIL_VERIFICATION_EXPIRES_IN="24h"
